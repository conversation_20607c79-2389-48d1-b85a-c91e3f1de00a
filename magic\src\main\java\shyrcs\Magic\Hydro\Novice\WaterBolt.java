package shyrcs.Magic.Hydro.Novice;

import org.bukkit.entity.Player;
import org.bukkit.entity.LivingEntity;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.ProjectileHitEvent;
import shyrcs.Magic.Spell;
import shyrcs.Manager.MonsterStatusManager;

public class WaterBolt implements Spell, Listener {
    
    @Override
    public String getName() {
        return "WaterBolt";
    }
    
    @Override
    public String getDescription() {
        return "Bắn một viên đạn nước về phía trước";
    }
    
    @Override
    public String getElement() {
        return "Hydro";
    }
    
    @Override
    public String getRank() {
        return "Novice";
    }
    
    @Override
    public int getManaCost() {
        return 15;
    }
    
    @Override
    public long getCooldown() {
        return 3000; // 3 giây
    }
    
    @Override
    public String getVelkathRune() {
        return "ᚹᚨᛏᛖᚱ ᛒᛟᛚᛏ";
    }
    
    @Override
    public String getChant() {
        return "wahtehr bohlt";
    }
    
    @Override
    public boolean canCast(Player caster) {
        // Ki<PERSON>m tra điều kiện c<PERSON> bản
        return true;
    }

    @Override
    public boolean cast(Player caster) {
        try {
            // Tạo hiệu ứng âm thanh
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ITEM_BUCKET_EMPTY, 1.0f, 1.2f);

            // Tạo snowball làm projectile (giống như water bolt)
            org.bukkit.entity.Snowball waterBolt = caster.launchProjectile(org.bukkit.entity.Snowball.class);
            waterBolt.setVelocity(caster.getLocation().getDirection().multiply(2.0));

            // Tạo hiệu ứng particle trail
            createWaterTrail(waterBolt);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Tạo hiệu ứng particle trail cho water bolt
     */
    private void createWaterTrail(org.bukkit.entity.Snowball waterBolt) {
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (waterBolt.isDead() || !waterBolt.isValid()) {
                    this.cancel();
                    return;
                }

                org.bukkit.Location loc = waterBolt.getLocation();

                // Hiệu ứng nước
                loc.getWorld().spawnParticle(org.bukkit.Particle.SPLASH, loc, 8, 0.2, 0.2, 0.2, 0.1);
                loc.getWorld().spawnParticle(org.bukkit.Particle.BUBBLE, loc, 3, 0.1, 0.1, 0.1, 0.02);
                loc.getWorld().spawnParticle(org.bukkit.Particle.DRIPPING_WATER, loc, 2, 0.1, 0.1, 0.1, 0);
            }
        }.runTaskTimer(org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 0L, 1L);
    }

    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        if (!(event.getEntity() instanceof org.bukkit.entity.Snowball)) return;

        org.bukkit.entity.Snowball snowball = (org.bukkit.entity.Snowball) event.getEntity();
        if (!(snowball.getShooter() instanceof Player)) return;

        Player caster = (Player) snowball.getShooter();

        // Áp dụng Hydro status cho monster bị hit
        if (event.getHitEntity() instanceof LivingEntity) {
            LivingEntity target = (LivingEntity) event.getHitEntity();
            if (!(target instanceof Player)) { // Không áp dụng cho player
                MonsterStatusManager.getInstance().applyElementalStatus(target, "Hydro", 10, caster);
                System.out.println("DEBUG - Applied Hydro status to " + target.getType());
            }
        }
    }
}
