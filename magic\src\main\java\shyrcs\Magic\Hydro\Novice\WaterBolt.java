package shyrcs.Magic.Hydro.Novice;

import org.bukkit.entity.Player;
import shyrcs.Magic.Spell;

public class WaterBolt implements Spell {
    
    @Override
    public String getName() {
        return "Water Bolt";
    }
    
    @Override
    public String getDescription() {
        return "Bắn một viên đạn nước về phía trước";
    }
    
    @Override
    public String getElement() {
        return "Hydro";
    }
    
    @Override
    public String getRank() {
        return "Novice";
    }
    
    @Override
    public int getManaCost() {
        return 15;
    }
    
    @Override
    public long getCooldown() {
        return 3000; // 3 giây
    }
    
    @Override
    public String getVelkathRune() {
        return "ᚹᚨᛏᛖᚱ ᛒᛟᛚᛏ";
    }
    
    @Override
    public String getChant() {
        return "wahtehr bohlt";
    }
    
    @Override
    public boolean canCast(Player caster) {
        // TODO: Implement water bolt logic
        return false;
    }
    
    @Override
    public boolean cast(Player caster) {
        // TODO: Implement water bolt casting
        caster.sendMessage("§b💧 Water Bolt sẽ được cập nhật trong phiên bản tới!");
        return false;
    }
}
