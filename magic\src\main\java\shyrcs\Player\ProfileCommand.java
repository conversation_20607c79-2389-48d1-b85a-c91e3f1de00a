package shyrcs.Player;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.Utils.HexColor;

public class ProfileCommand implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(HexColor.toLegacy("#ff6b6b", "Chỉ người chơi mới có thể sử dụng lệnh này!"));
            return true;
        }

        Player player = (Player) sender;

        // Mở GUI Profile
        ProfilePlayer profileGUI = new ProfilePlayer();
        profileGUI.openProfile(player);

        return true;
    }
}