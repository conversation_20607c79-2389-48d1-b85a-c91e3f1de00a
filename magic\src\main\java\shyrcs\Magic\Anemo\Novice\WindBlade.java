package shyrcs.Magic.Anemo.Novice;

import org.bukkit.entity.Player;
import shyrcs.Magic.Spell;

public class WindBlade implements Spell {
    
    @Override
    public String getName() {
        return "WindBlade";
    }
    
    @Override
    public String getDescription() {
        return "Tạo ra lưỡi gió sắc bén cắt qua kẻ địch";
    }
    
    @Override
    public String getElement() {
        return "Anemo";
    }
    
    @Override
    public String getRank() {
        return "Novice";
    }
    
    @Override
    public int getManaCost() {
        return 18;
    }
    
    @Override
    public long getCooldown() {
        return 4000; // 4 giây
    }
    
    @Override
    public String getVelkathRune() {
        return "ᚹᛁᚾᛞ ᛒᛚᚨᛞᛖ";
    }
    
    @Override
    public String getChant() {
        return "wihnd blahdeh";
    }
    
    @Override
    public boolean canCast(Player caster) {
        // Kiểm tra điều kiện cơ bản
        return true;
    }

    @Override
    public boolean cast(Player caster) {
        try {
            // Tạo hiệu ứng âm thanh
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.5f);

            // Tạo arrow làm projectile (giống như wind blade)
            org.bukkit.entity.Arrow windBlade = caster.launchProjectile(org.bukkit.entity.Arrow.class);
            windBlade.setVelocity(caster.getLocation().getDirection().multiply(2.5));
            windBlade.setPickupStatus(org.bukkit.entity.Arrow.PickupStatus.DISALLOWED);

            // Tạo hiệu ứng particle trail
            createWindTrail(windBlade);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Tạo hiệu ứng particle trail cho wind blade
     */
    private void createWindTrail(org.bukkit.entity.Arrow windBlade) {
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (windBlade.isDead() || !windBlade.isValid()) {
                    this.cancel();
                    return;
                }

                org.bukkit.Location loc = windBlade.getLocation();

                // Hiệu ứng gió
                loc.getWorld().spawnParticle(org.bukkit.Particle.SWEEP_ATTACK, loc, 1, 0.1, 0.1, 0.1, 0);
                loc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, loc, 3, 0.2, 0.2, 0.2, 0.02);
                loc.getWorld().spawnParticle(org.bukkit.Particle.WHITE_ASH, loc, 2, 0.1, 0.1, 0.1, 0.01);
            }
        }.runTaskTimer(org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 0L, 1L);
    }
}
