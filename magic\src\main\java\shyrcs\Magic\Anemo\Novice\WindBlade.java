package shyrcs.Magic.Anemo.Novice;

import org.bukkit.entity.Player;
import shyrcs.Magic.Spell;

public class WindBlade implements Spell {
    
    @Override
    public String getName() {
        return "Wind Blade";
    }
    
    @Override
    public String getDescription() {
        return "Tạo ra lưỡi gió sắc bén cắt qua kẻ địch";
    }
    
    @Override
    public String getElement() {
        return "Anemo";
    }
    
    @Override
    public String getRank() {
        return "Novice";
    }
    
    @Override
    public int getManaCost() {
        return 18;
    }
    
    @Override
    public long getCooldown() {
        return 4000; // 4 giây
    }
    
    @Override
    public String getVelkathRune() {
        return "ᚹᛁᚾᛞ ᛒᛚᚨᛞᛖ";
    }
    
    @Override
    public String getChant() {
        return "wihnd blahdeh";
    }
    
    @Override
    public boolean canCast(Player caster) {
        // TODO: Implement wind blade logic
        return false;
    }
    
    @Override
    public boolean cast(Player caster) {
        // TODO: Implement wind blade casting
        caster.sendMessage("§a🌪 Wind Blade sẽ được cập nhật trong phiên bản tới!");
        return false;
    }
}
