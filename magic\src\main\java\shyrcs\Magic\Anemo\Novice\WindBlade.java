package shyrcs.Magic.Anemo.Novice;

import org.bukkit.entity.Player;
import org.bukkit.entity.LivingEntity;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.ProjectileHitEvent;
import shyrcs.Magic.Spell;
import shyrcs.Manager.MonsterStatusManager;

public class WindBlade implements Spell, Listener {
    
    @Override
    public String getName() {
        return "WindBlade";
    }
    
    @Override
    public String getDescription() {
        return "Tạo ra lưỡi gió sắc bén cắt qua kẻ địch";
    }
    
    @Override
    public String getElement() {
        return "Anemo";
    }
    
    @Override
    public String getRank() {
        return "Novice";
    }
    
    @Override
    public int getManaCost() {
        return 18;
    }
    
    @Override
    public long getCooldown() {
        return 4000; // 4 giây
    }
    
    @Override
    public String getVelkathRune() {
        return "ᚹᛁᚾᛞ ᛒᛚᚨᛞᛖ";
    }
    
    @Override
    public String getChant() {
        return "wihnd blahdeh";
    }
    
    @Override
    public boolean canCast(Player caster) {
        // Ki<PERSON>m tra điều kiện cơ bản
        return true;
    }

    @Override
    public boolean cast(Player caster) {
        try {
            // Tạo hiệu ứng âm thanh
            caster.getWorld().playSound(caster.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.5f);

            // Tạo arrow làm projectile (giống như wind blade)
            org.bukkit.entity.Arrow windBlade = caster.launchProjectile(org.bukkit.entity.Arrow.class);
            windBlade.setVelocity(caster.getLocation().getDirection().multiply(2.5));
            windBlade.setPickupStatus(org.bukkit.entity.Arrow.PickupStatus.DISALLOWED);

            // Tạo hiệu ứng particle trail
            createWindTrail(windBlade);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Tạo hiệu ứng particle trail cho wind blade
     */
    private void createWindTrail(org.bukkit.entity.Arrow windBlade) {
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (windBlade.isDead() || !windBlade.isValid()) {
                    this.cancel();
                    return;
                }

                org.bukkit.Location loc = windBlade.getLocation();

                // Hiệu ứng gió
                loc.getWorld().spawnParticle(org.bukkit.Particle.SWEEP_ATTACK, loc, 1, 0.1, 0.1, 0.1, 0);
                loc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, loc, 3, 0.2, 0.2, 0.2, 0.02);
                loc.getWorld().spawnParticle(org.bukkit.Particle.WHITE_ASH, loc, 2, 0.1, 0.1, 0.1, 0.01);
            }
        }.runTaskTimer(org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 0L, 1L);
    }

    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        if (!(event.getEntity() instanceof org.bukkit.entity.Arrow)) return;

        org.bukkit.entity.Arrow arrow = (org.bukkit.entity.Arrow) event.getEntity();
        if (!(arrow.getShooter() instanceof Player)) return;

        Player caster = (Player) arrow.getShooter();

        // Áp dụng Anemo status cho monster bị hit
        if (event.getHitEntity() instanceof LivingEntity) {
            LivingEntity target = (LivingEntity) event.getHitEntity();
            if (!(target instanceof Player)) { // Không áp dụng cho player
                MonsterStatusManager.getInstance().applyElementalStatus(target, "Anemo", 10, caster);
                System.out.println("DEBUG - Applied Anemo status to " + target.getType());

                // Wind knockback effect
                org.bukkit.util.Vector knockback = target.getLocation().subtract(caster.getLocation()).toVector().normalize().multiply(0.8);
                knockback.setY(0.3); // Slight upward force
                target.setVelocity(knockback);
            }
        }

        // Wind explosion effect at hit location
        org.bukkit.Location hitLoc = arrow.getLocation();
        hitLoc.getWorld().spawnParticle(org.bukkit.Particle.SWEEP_ATTACK, hitLoc, 5, 1.0, 1.0, 1.0, 0);
        hitLoc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, hitLoc, 15, 1.0, 1.0, 1.0, 0.1);
        hitLoc.getWorld().playSound(hitLoc, org.bukkit.Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1.0f, 1.8f);
    }
}
