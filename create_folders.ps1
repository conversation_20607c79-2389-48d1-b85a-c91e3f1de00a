# Script tạo cấu trúc folder cho 10 nguyên tố

$elements = @("Hydro", "Anemo", "<PERSON><PERSON><PERSON>", "<PERSON>dr<PERSON>", "Cry<PERSON>", "G<PERSON>", "Light", "Dark", "Quantum")
$ranks = @("<PERSON><PERSON>", "Apprentice", "<PERSON>ept", "<PERSON><PERSON>", "Master", "<PERSON><PERSON>", "Mythic")

foreach ($element in $elements) {
    foreach ($rank in $ranks) {
        $path = "magic/src/main/java/shyrcs/Magic/$element/$rank"
        New-Item -ItemType Directory -Path $path -Force | Out-Null
        Write-Host "Created: $path"
    }
}

Write-Host "All element folders created successfully!"
