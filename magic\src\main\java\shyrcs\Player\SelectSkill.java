package shyrcs.Player;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import shyrcs.Utils.HexColor;

import java.util.Arrays;

@SuppressWarnings("deprecation")
public class SelectSkill implements Listener {

    /**
     * Mở GUI chọn skill cho đũa phép
     */
    public void openSelectSkill(Player player) {
        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        Inventory gui = Bukkit.createInventory(null, 27,
            HexColor.toLegacy("#4ecdc4", "🔮 <PERSON><PERSON>n Skill Cho <PERSON>"));

        // <PERSON><PERSON>n thị các phép đã học
        int slot = 0;
        for (String spellName : data.getLearnedSpells()) {
            if (slot >= 27) break;

            ItemStack spellItem = createSkillItem(spellName, data.getSelectedSkill().equals(spellName));
            gui.setItem(slot, spellItem);
            slot++;
        }

        player.openInventory(gui);
    }

    /**
     * Tạo item cho skill
     */
    private ItemStack createSkillItem(String spellName, boolean isSelected) {
        ItemStack item = new ItemStack(Material.BLAZE_POWDER);
        ItemMeta meta = item.getItemMeta();

        String displayName = HexColor.toLegacy(HexColor.PYRO, spellName);
        if (isSelected) {
            displayName = HexColor.toLegacy("#4caf50", "✓ ") + displayName;
            item.setType(Material.GLOWSTONE_DUST);
        }

        meta.setDisplayName(displayName);
        meta.setLore(Arrays.asList(
            "",
            isSelected ?
                HexColor.toLegacy("#4caf50", "▶ Đang được chọn cho đũa phép") :
                HexColor.toLegacy("#ffeb3b", "▶ Click để chọn cho đũa phép"),
            ""
        ));

        item.setItemMeta(meta);
        return item;
    }

    /**
     * Xử lý click trong GUI
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getView().getTitle().contains("Chọn Skill")) return;

        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();

        ItemStack item = event.getCurrentItem();
        if (item == null || item.getType() == Material.AIR) return;

        // Lấy tên skill từ display name
        String skillName = extractSkillName(item.getItemMeta().getDisplayName());

        // Cập nhật skill đã chọn
        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);
        data.setSelectedSkill(skillName);

        player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã chọn skill: " + skillName + " cho đũa phép!"));
        player.closeInventory();
    }

    /**
     * Trích xuất tên skill từ display name
     */
    private String extractSkillName(String displayName) {
        return displayName.replaceAll("§[0-9a-fk-or]", "").replaceAll("[✓ ]", "").trim();
    }
}
