package shyrcs.Player;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import net.kyori.adventure.text.Component;
import shyrcs.Utils.HexColor;

import java.util.Arrays;
import java.util.List;

public class ProfilePlayer implements Listener {

    /**
     * Mở GUI Profile cho người chơi
     */
    public void openProfile(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54,
            HexColor.toLegacy("#4ecdc4", "⚡ Magic Profile ⚡"));

        PlayerData playerData = new PlayerData();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        // Slot 4: Tên ng<PERSON>ời chơi + cấp độ
        ItemStack playerHead = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta headMeta = (SkullMeta) playerHead.getItemMeta();
        headMeta.setOwningPlayer(player);
        headMeta.setDisplayName(HexColor.toLegacy("#4ecdc4", "⚡ " + player.getName() + " ⚡"));
        headMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#ffeb3b", "Cấp độ: " + data.getLevel()),
            HexColor.toLegacy("#4fc3f7", "Kinh nghiệm: " + data.getExperience()),
            ""
        ));
        playerHead.setItemMeta(headMeta);
        gui.setItem(4, playerHead);

        // Slot 10: Thuộc tính (HP, Mana, Stamina)
        ItemStack attributes = new ItemStack(Material.TOTEM_OF_UNDYING);
        ItemMeta attrMeta = attributes.getItemMeta();
        attrMeta.setDisplayName(HexColor.toLegacy("#ff6b6b", "❤ Thuộc Tính"));
        attrMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#ff6b6b", "❤ HP: " + (int)player.getHealth() + "/" + (int)player.getMaxHealth()),
            HexColor.toLegacy("#4fc3f7", "✦ Mana: " + (int)data.getMana() + "/" + (int)data.getMaxMana()),
            HexColor.toLegacy("#4caf50", "⚡ Stamina: 100/100"),
            ""
        ));
        attributes.setItemMeta(attrMeta);
        gui.setItem(10, attributes);

        // Slot 12: Buffs / Debuffs
        ItemStack buffs = new ItemStack(Material.POTION);
        ItemMeta buffMeta = buffs.getItemMeta();
        buffMeta.setDisplayName(HexColor.toLegacy("#9c27b0", "🧪 Hiệu Ứng"));
        buffMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#4caf50", "✓ Không có hiệu ứng nào"),
            ""
        ));
        buffs.setItemMeta(buffMeta);
        gui.setItem(12, buffs);

        // Slot 14: Danh sách phép đã học (click để mở Spellbook)
        ItemStack spells = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta spellMeta = spells.getItemMeta();
        spellMeta.setDisplayName(HexColor.toLegacy("#ff9800", "📚 Phép Đã Học"));
        spellMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#4fc3f7", "Số phép đã học: " + data.getLearnedSpells().size()),
            HexColor.toLegacy("#ffeb3b", "Phép đang chọn: " + (data.getSelectedSkill().isEmpty() ? "Chưa chọn" : data.getSelectedSkill())),
            "",
            HexColor.toLegacy("#4caf50", "▶ Click để mở Spellbook")
        ));
        spells.setItemMeta(spellMeta);
        gui.setItem(14, spells);

        // Slot 16: Kỹ năng chiến đấu
        ItemStack combat = new ItemStack(Material.IRON_SWORD);
        ItemMeta combatMeta = combat.getItemMeta();
        combatMeta.setDisplayName(HexColor.toLegacy("#f44336", "⚔ Kỹ Năng Chiến Đấu"));
        combatMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#ff6b6b", "Quái vật đã tiêu diệt: " + data.getMonstersKilled()),
            HexColor.toLegacy("#4fc3f7", "Phép đã sử dụng: " + data.getSpellsUsed()),
            ""
        ));
        combat.setItemMeta(combatMeta);
        gui.setItem(16, combat);

        // Slot 20: Kháng nguyên tố
        ItemStack resistance = new ItemStack(Material.SHIELD);
        ItemMeta resMeta = resistance.getItemMeta();
        resMeta.setDisplayName(HexColor.toLegacy("#607d8b", "🛡 Kháng Nguyên Tố"));
        resMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#ff6b6b", "🔥 Lửa: " + data.getElementalResistances().getOrDefault("Fire", 0) + "%"),
            HexColor.toLegacy("#2196f3", "💧 Nước: " + data.getElementalResistances().getOrDefault("Water", 0) + "%"),
            HexColor.toLegacy("#4caf50", "🌿 Gió: " + data.getElementalResistances().getOrDefault("Air", 0) + "%"),
            HexColor.toLegacy("#795548", "🌍 Đất: " + data.getElementalResistances().getOrDefault("Earth", 0) + "%"),
            ""
        ));
        resistance.setItemMeta(resMeta);
        gui.setItem(20, resistance);

        // Slot 22: Thống kê tổng quát
        ItemStack stats = new ItemStack(Material.MAP);
        ItemMeta statMeta = stats.getItemMeta();
        statMeta.setDisplayName(HexColor.toLegacy("#009688", "📊 Thống Kê"));
        statMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#4fc3f7", "Thời gian chơi: " + formatTime(data.getPlayTime())),
            HexColor.toLegacy("#ffeb3b", "Nguyên tố chính: " + data.getSelectedElement()),
            ""
        ));
        stats.setItemMeta(statMeta);
        gui.setItem(22, stats);

        // Slot 24: Danh hiệu
        ItemStack titles = new ItemStack(Material.NAME_TAG);
        ItemMeta titleMeta = titles.getItemMeta();
        titleMeta.setDisplayName(HexColor.toLegacy("#e91e63", "🏆 Danh Hiệu"));
        titleMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#4fc3f7", "Danh hiệu hiện tại: Tân binh"),
            HexColor.toLegacy("#ffeb3b", "Tổng danh hiệu: " + data.getTitles().size()),
            ""
        ));
        titles.setItemMeta(titleMeta);
        gui.setItem(24, titles);

        // Rune slots (38, 39, 41, 42)
        createRuneSlot(gui, 38, "Rune Slot 1", Material.LAPIS_LAZULI);
        createRuneSlot(gui, 39, "Rune Slot 2", Material.GLOWSTONE_DUST);
        createRuneSlot(gui, 41, "Rune Slot 3", Material.REDSTONE);
        createRuneSlot(gui, 42, "Rune Slot 4", Material.QUARTZ);

        // Slot 49: Nút thoát
        ItemStack exit = new ItemStack(Material.BARRIER);
        ItemMeta exitMeta = exit.getItemMeta();
        exitMeta.setDisplayName(HexColor.toLegacy("#f44336", "❌ Đóng"));
        exit.setItemMeta(exitMeta);
        gui.setItem(49, exit);

        player.openInventory(gui);
    }

    /**
     * Tạo rune slot
     */
    private void createRuneSlot(Inventory gui, int slot, String name, Material material) {
        ItemStack rune = new ItemStack(material);
        ItemMeta runeMeta = rune.getItemMeta();
        runeMeta.setDisplayName(HexColor.toLegacy("#9c27b0", "🔮 " + name));
        runeMeta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy("#607d8b", "Trống"),
            HexColor.toLegacy("#4fc3f7", "Click để trang bị rune"),
            ""
        ));
        rune.setItemMeta(runeMeta);
        gui.setItem(slot, rune);
    }

    /**
     * Format thời gian
     */
    private String formatTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return hours + "h " + (minutes % 60) + "m";
        } else if (minutes > 0) {
            return minutes + "m " + (seconds % 60) + "s";
        } else {
            return seconds + "s";
        }
    }

    /**
     * Tạo ItemStack với Adventure API
     */
    private void setItemDisplayAndLore(ItemMeta meta, String displayName, String... loreLines) {
        meta.displayName(HexColor.createComponent(displayName));

        List<Component> loreComponents = Arrays.stream(loreLines)
            .map(HexColor::createComponent)
            .toList();
        meta.lore(loreComponents);
    }

    /**
     * Xử lý click trong GUI
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        String title = event.getView().title().toString();
        if (!title.contains("Magic Profile")) return;

        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();

        int slot = event.getSlot();

        switch (slot) {
            case 14: // Spellbook
                player.closeInventory();
                Spellbook spellbook = new Spellbook();
                spellbook.openSpellbook(player);
                break;

            case 49: // Đóng
                player.closeInventory();
                break;

            case 38: case 39: case 41: case 42: // Rune slots
                player.sendMessage(HexColor.toLegacy("#ffeb3b", "Tính năng Rune sẽ được cập nhật trong phiên bản tới!"));
                break;
        }
    }
}