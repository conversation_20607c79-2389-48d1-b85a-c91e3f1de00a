package shyrcs.Manager;

import org.bukkit.Bukkit;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.Utils.HexColor;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Quản lý hiển thị status nguyên tố trên đầu monsters
 * Format: <HP Monster> | <Element Type> <Countdown Timer>
 * <PERSON>hi không có nguyên tố thì chỉ hiển thị HP
 */
public class MonsterStatusManager {
    
    private static MonsterStatusManager instance;
    private final Map<UUID, ElementalStatus> elementalStatuses = new HashMap<>();
    private final Map<UUID, BukkitTask> updateTasks = new HashMap<>();
    
    public static MonsterStatusManager getInstance() {
        if (instance == null) {
            instance = new MonsterStatusManager();
        }
        return instance;
    }
    
    /**
     * Class lưu thông tin elemental status của monster
     */
    public static class ElementalStatus {
        public final String elementType;
        public final long endTime;
        public final Player caster;
        
        public ElementalStatus(String elementType, long endTime, Player caster) {
            this.elementType = elementType;
            this.endTime = endTime;
            this.caster = caster;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() >= endTime;
        }
        
        public int getRemainingSeconds() {
            long remaining = endTime - System.currentTimeMillis();
            return Math.max(0, (int) Math.ceil(remaining / 1000.0));
        }
    }
    
    /**
     * Áp dụng elemental status cho monster
     */
    public void applyElementalStatus(LivingEntity monster, String elementType, int durationSeconds, Player caster) {
        if (monster == null || monster.isDead()) return;
        
        UUID monsterId = monster.getUniqueId();
        long endTime = System.currentTimeMillis() + (durationSeconds * 1000L);
        
        ElementalStatus status = new ElementalStatus(elementType, endTime, caster);
        elementalStatuses.put(monsterId, status);
        
        // Bắt đầu update task nếu chưa có
        if (!updateTasks.containsKey(monsterId)) {
            startUpdateTask(monster);
        }
        
        updateMonsterName(monster);
    }
    
    /**
     * Xóa elemental status khỏi monster
     */
    public void removeElementalStatus(LivingEntity monster) {
        if (monster == null) return;
        
        UUID monsterId = monster.getUniqueId();
        elementalStatuses.remove(monsterId);
        
        // Dừng update task
        BukkitTask task = updateTasks.remove(monsterId);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }
        
        // Reset tên monster về chỉ hiển thị HP
        updateMonsterName(monster);
    }
    
    /**
     * Kiểm tra monster có elemental status không
     */
    public boolean hasElementalStatus(LivingEntity monster) {
        if (monster == null) return false;
        return elementalStatuses.containsKey(monster.getUniqueId());
    }
    
    /**
     * Lấy elemental status của monster
     */
    public ElementalStatus getElementalStatus(LivingEntity monster) {
        if (monster == null) return null;
        return elementalStatuses.get(monster.getUniqueId());
    }
    
    /**
     * Bắt đầu task cập nhật tên monster
     */
    private void startUpdateTask(LivingEntity monster) {
        UUID monsterId = monster.getUniqueId();
        
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (monster.isDead() || !monster.isValid()) {
                    removeElementalStatus(monster);
                    return;
                }
                
                ElementalStatus status = elementalStatuses.get(monsterId);
                if (status == null) {
                    this.cancel();
                    updateTasks.remove(monsterId);
                    return;
                }
                
                // Kiểm tra hết thời gian
                if (status.isExpired()) {
                    removeElementalStatus(monster);
                    return;
                }
                
                updateMonsterName(monster);
            }
        }.runTaskTimer(Bukkit.getPluginManager().getPlugin("Magic"), 0L, 10L); // Cập nhật mỗi 0.5 giây
        
        updateTasks.put(monsterId, task);
    }
      /**
     * Cập nhật tên hiển thị của monster
     */
    private void updateMonsterName(LivingEntity monster) {        // Tính toán HP hiện tại
        int currentHP = (int) Math.ceil(monster.getHealth());
        int maxHP = (int) Math.ceil(monster.getAttribute(Attribute.MAX_HEALTH).getValue());
        
        ElementalStatus status = elementalStatuses.get(monster.getUniqueId());
        
        String displayName;
        
        if (status == null || status.isExpired()) {
            // Chỉ hiển thị HP khi không có nguyên tố
            displayName = HexColor.toLegacy("#ff6b6b", "❤ " + currentHP + "/" + maxHP);
        } else {
            // Hiển thị HP | Element Type <Countdown>
            int remainingSeconds = status.getRemainingSeconds();
            String elementColor = getElementColor(status.elementType);
            
            displayName = 
                HexColor.toLegacy("#ff6b6b", "❤ " + currentHP + "/" + maxHP) +
                HexColor.toLegacy("#ffffff", " | ") +
                HexColor.toLegacy(elementColor, status.elementType) + 
                HexColor.toLegacy("#ffeb3b", " " + remainingSeconds + "s");        }
        
        monster.customName(HexColor.createComponent(displayName));
        monster.setCustomNameVisible(true);
    }
    
    /**
     * Lấy màu sắc cho element type
     */
    private String getElementColor(String elementType) {
        switch (elementType.toLowerCase()) {
            case "pyro":
            case "fire":
                return "#ff6b6b"; // Đỏ
            case "hydro":
            case "water":
                return "#4fc3f7"; // Xanh dương
            case "anemo":
            case "wind":
                return "#4caf50"; // Xanh lá
            case "electro":
            case "lightning":
                return "#9c27b0"; // Tím
            case "geo":
            case "earth":
                return "#ff9800"; // Cam
            case "cryo":
            case "ice":
                return "#00bcd4"; // Cyan
            case "dendro":
            case "nature":
                return "#8bc34a"; // Xanh lá nhạt
            case "steam":
            case "steam tornado":
                return "#87ceeb"; // Sky blue
            case "volcanic":
                return "#ff4500"; // Orange red
            case "hurricane":
                return "#7dd3c0"; // Teal
            case "boiling":
                return "#ff6347"; // Tomato
            default:
                return "#ffffff"; // Trắng mặc định
        }
    }
      /**
     * Force update tất cả monsters có status
     */
    public void updateAllMonsters() {
        // Hiện tại sẽ update qua task tự động
        // Có thể implement tìm monster theo UUID nếu cần thiết
    }
    
    /**
     * Dọn dẹp khi plugin disable
     */
    public void shutdown() {
        // Cancel tất cả tasks
        for (BukkitTask task : updateTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        updateTasks.clear();
        elementalStatuses.clear();
    }
}