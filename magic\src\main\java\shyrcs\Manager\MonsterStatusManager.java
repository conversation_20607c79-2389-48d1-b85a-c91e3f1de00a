package shyrcs.Manager;

import org.bukkit.Bukkit;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.Utils.MessageUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Quản lý hiển thị status nguyên tố trên đầu monsters
 * Format: <HP Monster> | <Element Type> <Countdown Timer>
 * <PERSON>hi không có nguyên tố thì chỉ hiển thị HP
 */
public class MonsterStatusManager {
    
    private static MonsterStatusManager instance;
    private final Map<UUID, ElementalStatus> elementalStatuses = new HashMap<>();
    private final Map<UUID, BukkitTask> updateTasks = new HashMap<>();
    
    public static MonsterStatusManager getInstance() {
        if (instance == null) {
            instance = new MonsterStatusManager();
        }
        return instance;
    }
    
    /**
     * Class lưu thông tin elemental status của monster
     */
    public static class ElementalStatus {
        public final String elementType;
        public final long endTime;
        public final Player caster;
        
        public ElementalStatus(String elementType, long endTime, Player caster) {
            this.elementType = elementType;
            this.endTime = endTime;
            this.caster = caster;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() >= endTime;
        }
        
        public int getRemainingSeconds() {
            long remaining = endTime - System.currentTimeMillis();
            return Math.max(0, (int) Math.ceil(remaining / 1000.0));
        }
    }
    
    /**
     * Áp dụng elemental status cho monster
     */
    public void applyElementalStatus(LivingEntity monster, String elementType, int durationSeconds, Player caster) {
        if (monster == null || monster.isDead()) return;

        UUID monsterId = monster.getUniqueId();

        // Kiểm tra phản ứng nguyên tố trước khi áp dụng
        ElementalStatus existingStatus = elementalStatuses.get(monsterId);
        if (existingStatus != null && !existingStatus.isExpired()) {
            // Có nguyên tố hiện tại, kiểm tra phản ứng
            if (checkElementalReaction(monster, existingStatus.elementType, elementType, caster)) {
                // Phản ứng đã xảy ra, xóa status cũ
                removeElementalStatus(monster);
                return;
            }
        }

        long endTime = System.currentTimeMillis() + (durationSeconds * 1000L);
        ElementalStatus status = new ElementalStatus(elementType, endTime, caster);
        elementalStatuses.put(monsterId, status);

        // Bắt đầu update task nếu chưa có
        if (!updateTasks.containsKey(monsterId)) {
            startUpdateTask(monster);
        }

        // Thêm hiệu ứng particles cho nguyên tố
        addElementalParticles(monster, elementType);

        updateMonsterName(monster);

        System.out.println("DEBUG - Applied " + elementType + " status to " + monster.getType() + " for " + durationSeconds + "s");
    }
    
    /**
     * Xóa elemental status khỏi monster
     */
    public void removeElementalStatus(LivingEntity monster) {
        if (monster == null) return;
        
        UUID monsterId = monster.getUniqueId();
        elementalStatuses.remove(monsterId);
        
        // Dừng update task
        BukkitTask task = updateTasks.remove(monsterId);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }
        
        // Reset tên monster về chỉ hiển thị HP
        updateMonsterName(monster);
    }
    
    /**
     * Kiểm tra monster có elemental status không
     */
    public boolean hasElementalStatus(LivingEntity monster) {
        if (monster == null) return false;
        return elementalStatuses.containsKey(monster.getUniqueId());
    }
    
    /**
     * Lấy elemental status của monster
     */
    public ElementalStatus getElementalStatus(LivingEntity monster) {
        if (monster == null) return null;
        return elementalStatuses.get(monster.getUniqueId());
    }
    
    /**
     * Bắt đầu task cập nhật tên monster
     */
    private void startUpdateTask(LivingEntity monster) {
        UUID monsterId = monster.getUniqueId();
        
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (monster.isDead() || !monster.isValid()) {
                    removeElementalStatus(monster);
                    return;
                }

                ElementalStatus status = elementalStatuses.get(monsterId);
                if (status == null) {
                    // Nếu không có elemental status, vẫn cập nhật HP nhưng với frequency thấp hơn
                    updateMonsterName(monster);
                    return;
                }

                // Kiểm tra hết thời gian
                if (status.isExpired()) {
                    removeElementalStatus(monster);
                    return;
                }

                updateMonsterName(monster);
            }
        }.runTaskTimer(Bukkit.getPluginManager().getPlugin("Magic"), 0L, 20L); // Cập nhật mỗi 1 giây (damage listener sẽ handle real-time)
        
        updateTasks.put(monsterId, task);
    }
    /**
     * Cập nhật tên hiển thị của monster
     */
    private void updateMonsterName(LivingEntity monster) {
        // Tính toán HP hiện tại (luôn cập nhật real-time)
        double currentHP = monster.getHealth();
        double maxHP = monster.getAttribute(Attribute.MAX_HEALTH).getValue();

        // Format HP với 1 chữ số thập phân nếu cần
        String hpDisplay = String.format("%.0f/%.0f", currentHP, maxHP);

        ElementalStatus status = elementalStatuses.get(monster.getUniqueId());

        String displayMessage;

        if (status == null || status.isExpired()) {
            // Chỉ hiển thị HP khi không có nguyên tố
            displayMessage = "<!italic><color:#ff6b6b>❤ " + hpDisplay + "</color>";
        } else {
            // Hiển thị HP | Element Type <Countdown>
            int remainingSeconds = status.getRemainingSeconds();
            String elementColor = getElementColor(status.elementType);

            displayMessage = "<!italic><color:#ff6b6b>❤ " + hpDisplay + "</color>" +
                           "<white> | </white>" +
                           "<color:" + elementColor + ">" + status.elementType + "</color>" +
                           "<color:#ffeb3b> " + remainingSeconds + "s</color>";
        }

        monster.customName(MessageUtil.getComponentParsed(displayMessage));
        monster.setCustomNameVisible(true);
    }
    
    /**
     * Kiểm tra và kích hoạt phản ứng nguyên tố
     */
    private boolean checkElementalReaction(LivingEntity monster, String existingElement, String newElement, Player caster) {
        String reaction = getElementalReaction(existingElement, newElement);

        if (reaction != null) {
            triggerElementalReaction(monster, reaction, existingElement, newElement, caster);
            return true;
        }

        return false;
    }

    /**
     * Lấy tên phản ứng nguyên tố (theo thứ tự element1 → element2)
     */
    private String getElementalReaction(String existingElement, String newElement) {
        // Normalize element names
        existingElement = existingElement.toLowerCase();
        newElement = newElement.toLowerCase();

        // VAPORIZE: Pyro (existing) + Hydro (new)
        if (existingElement.equals("pyro") && newElement.equals("hydro")) {
            return "VAPORIZE";
        }

        // STEAM CLOUD: Hydro (existing) + Pyro (new)
        if (existingElement.equals("hydro") && newElement.equals("pyro")) {
            return "STEAM_CLOUD";
        }

        // MELT: Pyro + Cryo hoặc Cryo + Pyro
        if ((existingElement.equals("pyro") && newElement.equals("cryo")) ||
            (existingElement.equals("cryo") && newElement.equals("pyro"))) {
            return "MELT";
        }

        // SWIRL: Anemo + bất kỳ nguyên tố nào khác
        if (existingElement.equals("anemo") || newElement.equals("anemo")) {
            return "SWIRL";
        }

        // ELECTRO-CHARGED: Electro + Hydro
        if ((existingElement.equals("electro") && newElement.equals("hydro")) ||
            (existingElement.equals("hydro") && newElement.equals("electro"))) {
            return "ELECTRO_CHARGED";
        }

        // OVERLOADED: Electro + Pyro
        if ((existingElement.equals("electro") && newElement.equals("pyro")) ||
            (existingElement.equals("pyro") && newElement.equals("electro"))) {
            return "OVERLOADED";
        }

        return null; // Không có phản ứng
    }

    /**
     * Kích hoạt phản ứng nguyên tố
     */
    private void triggerElementalReaction(LivingEntity monster, String reaction, String element1, String element2, Player caster) {
        System.out.println("DEBUG - Triggering " + reaction + " reaction: " + element1 + " + " + element2);

        switch (reaction) {
            case "VAPORIZE":
                triggerVaporize(monster, caster);
                break;
            case "STEAM_CLOUD":
                triggerSteamCloud(monster, caster);
                break;
            case "MELT":
                triggerMelt(monster, caster);
                break;
            case "SWIRL":
                triggerSwirl(monster, caster);
                break;
            case "ELECTRO_CHARGED":
                triggerElectroCharged(monster, caster);
                break;
            case "OVERLOADED":
                triggerOverloaded(monster, caster);
                break;
        }
    }

    /**
     * Kích hoạt phản ứng VAPORIZE
     */
    private void triggerVaporize(LivingEntity monster, Player caster) {
        // Damage bonus
        double maxHealth = monster.getAttribute(Attribute.MAX_HEALTH).getValue();
        double damage = maxHealth * 0.5; // 50% max HP damage
        monster.damage(damage, caster);

        // Visual effects
        org.bukkit.Location loc = monster.getLocation().add(0, 1, 0);

        // Steam particles
        loc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, loc, 20, 1.0, 1.0, 1.0, 0.1);
        loc.getWorld().spawnParticle(org.bukkit.Particle.SPLASH, loc, 15, 0.5, 0.5, 0.5, 0.2);

        // Sound effect
        loc.getWorld().playSound(loc, org.bukkit.Sound.BLOCK_FIRE_EXTINGUISH, 1.0f, 1.2f);

        // Message
        // caster.sendMessage(MessageUtil.hex("#87ceeb", "💨 Bốc hơi! Gây " + (int)damage + " sát thương!"));
        caster.sendMessage(MessageUtil.hex("#87ceeb", "💨 Bốc hơi!"));

        System.out.println("DEBUG - VAPORIZE triggered: " + (int)damage + " damage");
    }

    /**
     * Kích hoạt phản ứng STEAM CLOUD
     */
    private void triggerSteamCloud(LivingEntity monster, Player caster) {
        // Tạo đám mây hơi nước che tầm nhìn
        org.bukkit.Location loc = monster.getLocation().add(0, 1, 0);

        // Visual effects - nhiều steam hơn VAPORIZE
        loc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, loc, 50, 2.0, 2.0, 2.0, 0.2);
        loc.getWorld().spawnParticle(org.bukkit.Particle.WHITE_ASH, loc, 30, 1.5, 1.5, 1.5, 0.1);
        loc.getWorld().spawnParticle(org.bukkit.Particle.SPLASH, loc, 20, 1.0, 1.0, 1.0, 0.3);

        // Sound effect
        loc.getWorld().playSound(loc, org.bukkit.Sound.BLOCK_FIRE_EXTINGUISH, 1.5f, 0.8f);

        // Tạo hiệu ứng che tầm nhìn trong 5 giây
        createSteamCloudEffect(loc, 5);

        // Message
        caster.sendMessage(MessageUtil.hex("#b0e0e6", "☁️ STEAM CLOUD! Tạo đám mây hơi nước che tầm nhìn!"));

        System.out.println("DEBUG - STEAM CLOUD triggered");
    }

    /**
     * Tạo hiệu ứng đám mây hơi nước liên tục
     */
    private void createSteamCloudEffect(org.bukkit.Location center, int durationSeconds) {
        new org.bukkit.scheduler.BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = durationSeconds * 20; // Convert to ticks

            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    this.cancel();
                    return;
                }

                // Spawn steam particles mỗi tick
                center.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, center, 3, 1.5, 1.0, 1.5, 0.05);
                center.getWorld().spawnParticle(org.bukkit.Particle.WHITE_ASH, center, 2, 1.0, 0.5, 1.0, 0.02);

                ticks++;
            }
        }.runTaskTimer(org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 0L, 1L);
    }

    /**
     * Thêm hiệu ứng particles cho nguyên tố
     */
    private void addElementalParticles(LivingEntity monster, String elementType) {
        org.bukkit.Location loc = monster.getLocation().add(0, 1, 0);

        switch (elementType.toLowerCase()) {
            case "pyro":
                loc.getWorld().spawnParticle(org.bukkit.Particle.FLAME, loc, 5, 0.3, 0.3, 0.3, 0.02);
                break;
            case "hydro":
                loc.getWorld().spawnParticle(org.bukkit.Particle.SPLASH, loc, 5, 0.3, 0.3, 0.3, 0.1);
                break;
            case "anemo":
                loc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, loc, 3, 0.3, 0.3, 0.3, 0.02);
                break;
            case "electro":
                loc.getWorld().spawnParticle(org.bukkit.Particle.ELECTRIC_SPARK, loc, 5, 0.3, 0.3, 0.3, 0.1);
                break;
            case "cryo":
                loc.getWorld().spawnParticle(org.bukkit.Particle.SNOWFLAKE, loc, 5, 0.3, 0.3, 0.3, 0.02);
                break;
        }
    }

    /**
     * Placeholder methods cho các phản ứng khác
     */
    private void triggerMelt(LivingEntity monster, Player caster) {
        // TODO: Implement MELT reaction
        caster.sendMessage(MessageUtil.hex("#ff9800", "🔥 MELT reaction!"));
    }

    private void triggerSwirl(LivingEntity monster, Player caster) {
        // TODO: Implement SWIRL reaction
        caster.sendMessage(MessageUtil.hex("#4caf50", "🌪️ SWIRL reaction!"));
    }

    private void triggerElectroCharged(LivingEntity monster, Player caster) {
        // TODO: Implement ELECTRO-CHARGED reaction
        caster.sendMessage(MessageUtil.hex("#9c27b0", "⚡ ELECTRO-CHARGED reaction!"));
    }

    private void triggerOverloaded(LivingEntity monster, Player caster) {
        // TODO: Implement OVERLOADED reaction
        caster.sendMessage(MessageUtil.hex("#ff4500", "💥 OVERLOADED reaction!"));
    }

    /**
     * Lấy màu sắc cho element type
     */
    private String getElementColor(String elementType) {
        switch (elementType.toLowerCase()) {
            case "pyro":
            case "fire":
                return "#ff6b6b"; // Đỏ
            case "hydro":
            case "water":
                return "#4fc3f7"; // Xanh dương
            case "anemo":
            case "wind":
                return "#4caf50"; // Xanh lá
            case "electro":
            case "lightning":
                return "#9c27b0"; // Tím
            case "geo":
            case "earth":
                return "#ff9800"; // Cam
            case "cryo":
            case "ice":
                return "#00bcd4"; // Cyan
            case "dendro":
            case "nature":
                return "#8bc34a"; // Xanh lá nhạt
            case "steam":
            case "steam tornado":
                return "#87ceeb"; // Sky blue
            case "volcanic":
                return "#ff4500"; // Orange red
            case "hurricane":
                return "#7dd3c0"; // Teal
            case "boiling":
                return "#ff6347"; // Tomato
            default:
                return "#ffffff"; // Trắng mặc định
        }
    }

    /**
     * Đảm bảo monster luôn hiển thị tên (ngay cả khi không có status)
     */
    public void ensureMonsterNameVisible(LivingEntity monster) {
        if (monster == null || monster.isDead() || monster instanceof Player) return;

        UUID monsterId = monster.getUniqueId();

        // Luôn cập nhật tên và hiển thị
        updateMonsterName(monster);

        // Bắt đầu task để cập nhật HP real-time (ngay cả khi không có elemental status)
        if (!updateTasks.containsKey(monsterId)) {
            startUpdateTask(monster);
        }
    }

    /**
     * Force update HP ngay lập tức (được gọi từ damage listener)
     */
    public void forceUpdateHP(LivingEntity monster) {
        if (monster == null || monster.isDead() || monster instanceof Player) return;

        // Cập nhật tên ngay lập tức
        updateMonsterName(monster);

        // Đảm bảo tên được hiển thị
        monster.setCustomNameVisible(true);

        // Debug
        double hp = monster.getHealth();
        double maxHP = monster.getAttribute(Attribute.MAX_HEALTH).getValue();
        System.out.println("DEBUG - Force updated " + monster.getType() + " HP: " + String.format("%.1f/%.1f", hp, maxHP));
    }

    /**
     * Force update tất cả monsters có status
     */
    public void updateAllMonsters() {
        // Hiện tại sẽ update qua task tự động
        // Có thể implement tìm monster theo UUID nếu cần thiết
    }
    
    /**
     * Dọn dẹp khi plugin disable
     */
    public void shutdown() {
        // Cancel tất cả tasks
        for (BukkitTask task : updateTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        updateTasks.clear();
        elementalStatuses.clear();
    }
}