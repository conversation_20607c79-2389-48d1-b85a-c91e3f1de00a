package shyrcs.Wand;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import io.papermc.paper.event.player.AsyncChatEvent;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;
import org.bukkit.inventory.ItemStack;
import shyrcs.Player.PlayerData;
import shyrcs.Player.Spellbook;
import shyrcs.Utils.HexColor;
import shyrcs.Magic.Spell;
import shyrcs.Manager.SpellRegistry;
import shyrcs.Manager.ActionBarManager;

public class WoodWand implements Listener {

    private final SpellRegistry spellRegistry;

    public WoodWand() {
        this.spellRegistry = new SpellRegistry();
    }

    /**
     * Xử lý chuột phải với đũa phép
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();

        if (item == null || !isWand(item)) return;

        // Kiểm tra signature
        if (!canUseWand(player, item)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn không thể sử dụng đũa phép này! Đây không phải đũa của bạn."));
            return;
        }

        if (event.getAction().toString().contains("RIGHT_CLICK")) {
            event.setCancelled(true);

            // Mở Spellbook
            Spellbook spellbook = new Spellbook();
            spellbook.openSpellbook(player);
        }
    }

    /**
     * Xử lý chat niệm chú
     */
    @EventHandler
    public void onPlayerChat(AsyncChatEvent event) {
        Player player = event.getPlayer();
        String message = PlainTextComponentSerializer.plainText().serialize(event.message()).toLowerCase();

        // Kiểm tra có đang cầm đũa phép không
        ItemStack item = player.getInventory().getItemInMainHand();
        if (item == null || !isWand(item)) return;

        // Kiểm tra signature
        if (!canUseWand(player, item)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn không thể sử dụng đũa phép này! Đây không phải đũa của bạn."));
            return;
        }

        // Lấy dữ liệu người chơi
        PlayerData playerData = new PlayerData();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        String selectedSpell = data.getSelectedSkill();
        if (selectedSpell == null || selectedSpell.isEmpty()) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn chưa chọn phép nào! Chuột phải để mở Spellbook."));
            return;
        }

        // Kiểm tra niệm chú
        if (isValidChant(message, selectedSpell)) {
            event.setCancelled(true);

            // Thi triển phép
            castSpell(player, selectedSpell);
        }
    }

    /**
     * Kiểm tra có phải đũa phép không
     */
    @SuppressWarnings("deprecation")
    private boolean isWand(ItemStack item) {
        if (item.getItemMeta() == null) return false;

        String displayName = item.getItemMeta().getDisplayName();
        return displayName != null && displayName.contains("Đũa Phép");
    }

    /**
     * Kiểm tra player có thể sử dụng đũa phép này không (dựa trên signature)
     */
    @SuppressWarnings("deprecation")
    private boolean canUseWand(Player player, ItemStack wand) {
        if (wand.getItemMeta() == null || wand.getItemMeta().getLore() == null) return true;

        for (String lore : wand.getItemMeta().getLore()) {
            if (lore.contains("Signature:")) {
                // Lấy signature từ lore
                String signature = lore.replace("Signature:", "").trim();
                // Loại bỏ color codes
                signature = signature.replaceAll("§[0-9a-fk-or]", "").trim();

                // Kiểm tra signature có khớp với tên player không
                return signature.equalsIgnoreCase(player.getName());
            }
        }

        // Nếu không có signature thì ai cũng dùng được
        return true;
    }

    /**
     * Kiểm tra niệm chú có đúng không
     */
    private boolean isValidChant(String message, String spellName) {
        switch (spellName) {
            case "FlameLance":
                return message.equals("flahmeh lahnkeh");
            case "WaterBolt":
                return message.equals("wahtehr bohlt");
            case "WindBlade":
                return message.equals("wihnd blahdeh");
            default:
                return false;
        }
    }

    /**
     * Thi triển phép thuật
     */
    private void castSpell(Player player, String spellName) {
        Spell spell = getSpellInstance(spellName);
        if (spell == null) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Lỗi: Không tìm thấy phép " + spellName));
            return;
        }

        // Kiểm tra điều kiện
        if (!spell.canCast(player)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không thể thi triển phép lúc này!"));
            return;
        }

        // Kiểm tra cooldown
        PlayerData playerData = new PlayerData();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        if (data.isOnCooldown(spellName)) {
            long remaining = data.getRemainingCooldown(spellName);
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Phép đang hồi chiêu! Còn " + (remaining / 1000) + "s"));
            return;
        }

        // Kiểm tra mana
        if (data.getMana() < getSpellManaCost(spellName)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không đủ mana! Cần " + getSpellManaCost(spellName) + " mana."));
            return;
        }

        // Thi triển phép
        if (spell.cast(player)) {
            // Trừ mana
            data.setMana((int)(data.getMana() - getSpellManaCost(spellName)));

            // Set cooldown
            data.setCooldown(spellName, getSpellCooldown(spellName));

            // Tăng số phép đã sử dụng
            data.setSpellsUsed(data.getSpellsUsed() + 1);

            // Cập nhật action bar ngay lập tức
            ActionBarManager.getInstance().updateImmediately(player);

            player.sendMessage(HexColor.toLegacy("#4caf50", "✨ Đã thi triển phép " + spellName + "!"));
        } else {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Thi triển phép thất bại!"));
        }
    }

    /**
     * Lấy instance của phép thuật
     */
    private Spell getSpellInstance(String spellName) {
        return spellRegistry.getSpell(spellName);
    }

    /**
     * Lấy mana cost của phép
     */
    private int getSpellManaCost(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return 20;
            case "WaterBolt":
                return 15;
            case "WindBlade":
                return 18;
            default:
                return 0;
        }
    }

    /**
     * Lấy cooldown của phép
     */
    private long getSpellCooldown(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return 5000; // 5 giây
            case "WaterBolt":
                return 3000; // 3 giây
            case "WindBlade":
                return 4000; // 4 giây
            default:
                return 0;
        }
    }
}