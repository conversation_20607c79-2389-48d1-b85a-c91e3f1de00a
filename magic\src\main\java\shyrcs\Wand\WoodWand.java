package shyrcs.Wand;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import io.papermc.paper.event.player.AsyncChatEvent;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;
import org.bukkit.inventory.ItemStack;
import shyrcs.Player.PlayerData;
import shyrcs.Player.Spellbook;
import shyrcs.Utils.HexColor;
import shyrcs.Magic.Spell;
import shyrcs.Manager.SpellRegistry;
import shyrcs.Manager.ActionBarManager;

public class WoodWand implements Listener {

    private final SpellRegistry spellRegistry;

    public WoodWand() {
        this.spellRegistry = new SpellRegistry();
    }

    /**
     * Xử lý chuột phải với đũa phép
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();

        if (item == null || !isWand(item)) return;

        // Kiểm tra signature
        if (!canUseWand(player, item)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn không thể sử dụng đũa phép này! Đây không phải đũa của bạn."));
            return;
        }

        if (event.getAction().toString().contains("RIGHT_CLICK")) {
            event.setCancelled(true);

            // Mở Spellbook
            Spellbook spellbook = new Spellbook();
            spellbook.openSpellbook(player);
        }
    }

    /**
     * Xử lý chat niệm chú
     */
    @EventHandler
    public void onPlayerChat(AsyncChatEvent event) {
        Player player = event.getPlayer();
        String message = PlainTextComponentSerializer.plainText().serialize(event.message()).toLowerCase();

        // Kiểm tra có đang cầm đũa phép không
        ItemStack item = player.getInventory().getItemInMainHand();
        if (item == null || !isWand(item)) return;

        // Kiểm tra signature
        if (!canUseWand(player, item)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn không thể sử dụng đũa phép này! Đây không phải đũa của bạn."));
            return;
        }

        // Lấy dữ liệu người chơi
        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        String selectedSpell = data.getSelectedSkill();
        System.out.println("DEBUG - Selected spell from data: '" + selectedSpell + "'");

        // Clean spell name từ color codes
        selectedSpell = cleanSpellName(selectedSpell);
        System.out.println("DEBUG - Cleaned spell name: '" + selectedSpell + "'");

        if (selectedSpell == null || selectedSpell.isEmpty()) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn chưa chọn phép nào! Chuột phải để mở Spellbook."));
            return;
        }

        // Kiểm tra niệm chú
        if (isValidChant(message, selectedSpell)) {
            event.setCancelled(true);

            // Thi triển phép trên main thread để tránh async errors
            String finalSpellName = selectedSpell;
            org.bukkit.Bukkit.getScheduler().runTask(
                org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
                () -> castSpell(player, finalSpellName)
            );
        } else {
            // Hiển thị chant đúng nếu sai
            String correctChant = getCorrectChant(selectedSpell);
            if (!correctChant.equals("???")) {
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "Niệm chú sai! Niệm chú đúng: ") +
                                 HexColor.toLegacy("#ffeb3b", correctChant));
            }
        }
    }

    /**
     * Kiểm tra có phải đũa phép không
     */
    @SuppressWarnings("deprecation")
    private boolean isWand(ItemStack item) {
        if (item.getItemMeta() == null) return false;

        String displayName = item.getItemMeta().getDisplayName();
        return displayName != null && displayName.contains("Đũa Phép");
    }

    /**
     * Kiểm tra player có thể sử dụng đũa phép này không (dựa trên signature)
     */
    @SuppressWarnings("deprecation")
    private boolean canUseWand(Player player, ItemStack wand) {
        if (wand.getItemMeta() == null || wand.getItemMeta().getLore() == null) {
            System.out.println("DEBUG - No meta or lore, allowing use");
            return true;
        }

        String playerName = player.getName();
        System.out.println("DEBUG - Checking wand for player: " + playerName);

        for (String lore : wand.getItemMeta().getLore()) {
            // Loại bỏ tất cả color codes (legacy và hex)
            String cleanLore = lore.replaceAll("§[0-9a-fk-or]", ""); // Legacy colors
            cleanLore = cleanLore.replaceAll("§x(§[0-9A-Fa-f]){6}", ""); // Hex colors
            cleanLore = cleanLore.replaceAll("§.", ""); // Bất kỳ color code nào khác
            cleanLore = cleanLore.trim();

            System.out.println("DEBUG - Original lore: '" + lore + "'");
            System.out.println("DEBUG - Clean lore line: '" + cleanLore + "'");

            if (cleanLore.contains("Signature:")) {
                // Lấy signature từ lore
                String signature = cleanLore.substring(cleanLore.indexOf("Signature:") + "Signature:".length()).trim();

                System.out.println("DEBUG - Found signature: '" + signature + "' for player: '" + playerName + "'");

                // Kiểm tra signature có khớp với tên player không
                boolean canUse = signature.equalsIgnoreCase(playerName);
                System.out.println("DEBUG - Can use wand: " + canUse);

                return canUse;
            }
        }

        // Nếu không có signature thì ai cũng dùng được
        System.out.println("DEBUG - No signature found in lore, allowing use");
        return true;
    }

    /**
     * Kiểm tra niệm chú có đúng không
     */
    private boolean isValidChant(String message, String spellName) {
        System.out.println("DEBUG - isValidChant called with message: '" + message + "', spellName: '" + spellName + "'");

        if (spellName == null) {
            System.out.println("DEBUG - spellName is null in isValidChant");
            return false;
        }

        switch (spellName) {
            case "FlameLance":
                boolean flameLanceMatch = message.equals("flahmeh lahnkeh");
                System.out.println("DEBUG - FlameLance chant match: " + flameLanceMatch);
                return flameLanceMatch;
            case "WaterBolt":
                boolean waterBoltMatch = message.equals("wahtehr bohlt");
                System.out.println("DEBUG - WaterBolt chant match: " + waterBoltMatch);
                return waterBoltMatch;
            case "WindBlade":
                boolean windBladeMatch = message.equals("wihnd blahdeh");
                System.out.println("DEBUG - WindBlade chant match: " + windBladeMatch);
                return windBladeMatch;
            default:
                System.out.println("DEBUG - No chant match found for spell: '" + spellName + "'");
                return false;
        }
    }

    /**
     * Thi triển phép thuật
     */
    private void castSpell(Player player, String spellName) {
        // Clean spell name trước khi sử dụng
        spellName = cleanSpellName(spellName);
        System.out.println("DEBUG - castSpell with cleaned name: '" + spellName + "'");

        Spell spell = getSpellInstance(spellName);
        System.out.println("DEBUG - getSpellInstance('" + spellName + "') returned: " + spell);

        if (spell == null) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Lỗi: Không tìm thấy phép " + spellName));
            System.out.println("DEBUG - Spell not found in registry: " + spellName);
            return;
        }

        // Kiểm tra điều kiện
        if (!spell.canCast(player)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không thể thi triển phép lúc này!"));
            return;
        }

        // Kiểm tra cooldown
        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        if (data.isOnCooldown(spellName)) {
            long remaining = data.getRemainingCooldown(spellName);
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Phép đang hồi chiêu! Còn " + (remaining / 1000) + "s"));
            return;
        }

        // Kiểm tra mana
        if (data.getMana() < getSpellManaCost(spellName)) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không đủ mana! Cần " + getSpellManaCost(spellName) + " mana."));
            return;
        }

        // Thi triển phép
        if (spell.cast(player)) {
            // Trừ mana
            data.setMana((int)(data.getMana() - getSpellManaCost(spellName)));

            // Set cooldown
            data.setCooldown(spellName, getSpellCooldown(spellName));

            // Tăng số phép đã sử dụng
            data.setSpellsUsed(data.getSpellsUsed() + 1);

            // Cập nhật action bar ngay lập tức
            ActionBarManager.getInstance().updateImmediately(player);

            player.sendMessage(HexColor.toLegacy("#4caf50", "✨ Đã thi triển phép " + spellName + "!"));
        } else {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Thi triển phép thất bại!"));
        }
    }

    /**
     * Lấy instance của phép thuật
     */
    private Spell getSpellInstance(String spellName) {
        return spellRegistry.getSpell(spellName);
    }

    /**
     * Lấy mana cost của phép
     */
    private int getSpellManaCost(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return 20;
            case "WaterBolt":
                return 15;
            case "WindBlade":
                return 18;
            default:
                return 0;
        }
    }

    /**
     * Lấy cooldown của phép
     */
    private long getSpellCooldown(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return 5000; // 5 giây
            case "WaterBolt":
                return 3000; // 3 giây
            case "WindBlade":
                return 4000; // 4 giây
            default:
                return 0;
        }
    }

    /**
     * Clean spell name từ color codes
     */
    private String cleanSpellName(String spellName) {
        if (spellName == null) return null;

        // Loại bỏ tất cả color codes
        String clean = spellName.replaceAll("§[0-9a-fk-or]", ""); // Legacy colors
        clean = clean.replaceAll("§x(§[0-9A-Fa-f]){6}", ""); // Hex colors
        clean = clean.replaceAll("§.", ""); // Bất kỳ color code nào khác
        clean = clean.trim();

        System.out.println("DEBUG - cleanSpellName: '" + spellName + "' -> '" + clean + "'");
        return clean;
    }

    /**
     * Lấy niệm chú đúng cho spell
     */
    private String getCorrectChant(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return "flahmeh lahnkeh";
            case "WaterBolt":
                return "wahtehr bohlt";
            case "WindBlade":
                return "wihnd blahdeh";
            default:
                return "???";
        }
    }
}