package shyrcs.Listeners;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.CreatureSpawnEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import shyrcs.Manager.MonsterStatusManager;

/**
 * Listener để tự động hiển thị tên cho tất cả monsters
 */
public class MonsterSpawnListener implements Listener {
    
    /**
     * Khi monster spawn, tự động hiển thị tên
     */
    @EventHandler
    public void onCreatureSpawn(CreatureSpawnEvent event) {
        LivingEntity entity = event.getEntity();
        
        // Chỉ áp dụng cho monsters, không phải players
        if (!(entity instanceof Player)) {
            // Delay một chút để đảm bảo entity đã spawn hoàn toàn
            org.bukkit.Bukkit.getScheduler().runTaskLater(
                org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 
                () -> {
                    if (entity.isValid() && !entity.isDead()) {
                        MonsterStatusManager.getInstance().ensureMonsterNameVisible(entity);
                        // System.out.println("DEBUG - Auto-enabled name display for " + entity.getType());
                    }
                }, 
                5L // 0.25 giây delay
            );
        }
    }
    
    /**
     * Khi player join, hiển thị tên cho tất cả monsters gần đó
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Delay để player load hoàn toàn
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 
            () -> {
                // Tìm tất cả monsters trong vùng 50 blocks
                for (org.bukkit.entity.Entity entity : player.getNearbyEntities(50, 50, 50)) {
                    if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                        LivingEntity monster = (LivingEntity) entity;
                        MonsterStatusManager.getInstance().ensureMonsterNameVisible(monster);
                    }
                }
                System.out.println("DEBUG - Enabled name display for nearby monsters for " + player.getName());
            }, 
            40L // 2 giây delay
        );
    }
}
