package shyrcs.Manager;

import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.Player.PlayerData;
import shyrcs.Utils.HexColor;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Quản lý hiển thị action bar cho người chơi
 * Hiển thị: HP | Mana | Tên phép đang áp dụng cho gậy
 */
public class ActionBarManager {
    
    private static ActionBarManager instance;
    private final Map<UUID, BukkitTask> activeTasks = new HashMap<>();
    private final PlayerData playerData;
    
    public ActionBarManager() {
        this.playerData = PlayerData.getInstance();
    }
    
    public static ActionBarManager getInstance() {
        if (instance == null) {
            instance = new ActionBarManager();
        }
        return instance;
    }
    
    /**
     * Bắt đầu hiển thị action bar cho người chơi
     */
    public void startActionBar(Player player) {
        if (player == null || !player.isOnline()) return;
        
        UUID uuid = player.getUniqueId();
        
        // Dừng task cũ nếu có
        stopActionBar(player);
        
        // Tạo task mới
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (!player.isOnline()) {
                    this.cancel();
                    activeTasks.remove(uuid);
                    return;
                }
                
                updateActionBar(player);
            }
        }.runTaskTimer(Bukkit.getPluginManager().getPlugin("Magic"), 0L, 20L); // Cập nhật mỗi giây
        
        activeTasks.put(uuid, task);
    }
    
    /**
     * Dừng hiển thị action bar cho người chơi
     */
    public void stopActionBar(Player player) {
        if (player == null) return;
        
        UUID uuid = player.getUniqueId();
        BukkitTask task = activeTasks.remove(uuid);
        
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }
        
        // Xóa action bar
        if (player.isOnline()) {
            player.sendActionBar(Component.empty());
        }
    }
    
    /**
     * Cập nhật nội dung action bar và hồi phục mana
     */
    private void updateActionBar(Player player) {
        try {
            // Kiểm tra có buff notifications không - nếu có thì BuffNotificationManager sẽ xử lý
            BuffNotificationManager buffManager = BuffNotificationManager.getInstance();
            if (buffManager.hasActiveNotifications(player)) {
                return; // Không hiển thị action bar bình thường khi có buff notifications
            }
            
            PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

            // Hồi phục mana (5 mana/giây)
            regenerateMana(data);

            // Lấy thông tin HP
            int currentHP = (int) player.getHealth();
            int maxHP = 20; // Minecraft default max health

            // Lấy thông tin Mana
            int currentMana = (int) data.getMana();
            int maxMana = (int) data.getMaxMana();

            // Lấy tên phép đang chọn
            String selectedSpell = data.getSelectedSkill();
            if (selectedSpell == null || selectedSpell.isEmpty()) {
                selectedSpell = "Chưa chọn";
            }

            // Tạo action bar text với màu sắc
            String actionBarText =
                HexColor.toLegacy("#ff6b6b", "❤ " + currentHP + "/" + maxHP) +
                HexColor.toLegacy("#ffffff", " | ") +
                HexColor.toLegacy("#4fc3f7", "✦ " + currentMana + "/" + maxMana) +
                HexColor.toLegacy("#ffffff", " | ") +
                HexColor.toLegacy("#ffeb3b", "🔮 " + selectedSpell);

            // Gửi action bar
            Component actionBarComponent = HexColor.createComponent(actionBarText);
            player.sendActionBar(actionBarComponent);

        } catch (Exception e) {
            // Nếu có lỗi, dừng action bar cho player này
            stopActionBar(player);
        }
    }

    /**
     * Hồi phục mana cho player (5 mana/giây)
     */
    private void regenerateMana(PlayerData.PlayerMagicData data) {
        double currentMana = data.getMana();
        double maxMana = data.getMaxMana();

        // Chỉ hồi phục nếu chưa đầy mana
        if (currentMana < maxMana) {
            double newMana = Math.min(currentMana + 5, maxMana);
            data.setMana((int) newMana);

            // Debug (có thể tắt sau)
            // System.out.println("DEBUG - Mana regen: " + (int)currentMana + " -> " + (int)newMana);
        }
    }
    
    /**
     * Bắt đầu action bar cho tất cả người chơi online
     */
    public void startForAllPlayers() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            startActionBar(player);
        }
    }
    
    /**
     * Dừng action bar cho tất cả người chơi
     */
    public void stopForAllPlayers() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            stopActionBar(player);
        }
        activeTasks.clear();
    }
    
    /**
     * Kiểm tra player có đang hiển thị action bar không
     */
    public boolean isActive(Player player) {
        return activeTasks.containsKey(player.getUniqueId());
    }
    
    /**
     * Cập nhật ngay lập tức action bar cho player (không chờ timer)
     */
    public void updateImmediately(Player player) {
        if (isActive(player)) {
            updateActionBar(player);
        }
    }
    
    /**
     * Dọn dẹp khi plugin disable
     */
    public void shutdown() {
        stopForAllPlayers();
    }
}
