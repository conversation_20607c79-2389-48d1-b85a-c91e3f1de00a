package shyrcs.ReactionElemental;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.Bukkit;
import shyrcs.Manager.BuffNotificationManager;
import shyrcs.Manager.MonsterStatusManager;
import shyrcs.Player.PlayerData;
import shyrcs.Utils.HexColor;

/**
 * Hệ thống phản ứng giữa Pyro và Hydro
 * 
 * COMBO EXAMPLES:
 * 1. VAPORIZE (Pyro → Hydro): 
 *    - Cast FlameLance, sau đó cast WaterBolt trong 3 giây
 *    - Kết quả: Tăng 50% sát thương Pyro trong 10 giây tiếp theo
 * 
 * 2. STEAM CLOUD (Hydro → Pyro): 
 *    - Cast WaterBolt, sau đó cast FlameLance trong 3 giây  
 *    - Kết quả: Tạo đám mây hơi nước che tầm nhìn 5 giây
 * 
 * 3. BOILING WATER (Đồng thời): 
 *    - Cast cả 2 phép trong vòng 1 giây
 *    - Kết quả: Tạo vùng nước sôi gây 1❤/s trong 10 giây
 */
public class PyroxHydro {
    
    private static final long COMBO_WINDOW = 3000; // 3 giây để thực hiện combo
    
    /**
     * Enum định nghĩa các loại phản ứng
     */
    public enum ReactionType {
        VAPORIZE("Vaporize", "#ff6b6b", "Bốc hơi - Tăng sát thương Pyro"),
        STEAM_CLOUD("Steam Cloud", "#87ceeb", "Đám mây hơi nước - Che tầm nhìn"),
        BOILING_WATER("Boiling Water", "#ff4500", "Nước sôi - Sát thương liên tục");
        
        private final String name;
        private final String color;
        private final String description;
        
        ReactionType(String name, String color, String description) {
            this.name = name;
            this.color = color;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getColor() { return color; }
        public String getDescription() { return description; }
    }
    
    /**
     * Kiểm tra và kích hoạt combo khi người chơi cast phép
     */
    public static void checkAndTriggerCombo(Player player, String spellName, Location targetLocation) {
        PlayerData.PlayerMagicData data = PlayerData.getInstance().getPlayerData(player);
        String element = getSpellElement(spellName);
        
        if (!element.equals("Pyro") && !element.equals("Hydro")) {
            return; // Không phải phép Pyro/Hydro
        }
        
        // Lấy phép thuật trước đó
        String lastSpell = data.getLastCastedSpell();
        String lastElement = getSpellElement(lastSpell);
        long lastCastTime = data.getLastCastTime();
        
        // Kiểm tra combo window
        if (System.currentTimeMillis() - lastCastTime > COMBO_WINDOW) {
            // Quá thời gian combo, chỉ lưu spell hiện tại
            data.setLastCastedSpell(spellName);
            data.setLastCastTime(System.currentTimeMillis());
            return;
        }
        
        // Kiểm tra combo
        ReactionType reaction = null;
        
        if (lastElement.equals("Pyro") && element.equals("Hydro")) {
            reaction = ReactionType.VAPORIZE;
        } else if (lastElement.equals("Hydro") && element.equals("Pyro")) {
            reaction = ReactionType.STEAM_CLOUD;
        } else if (element.equals("Pyro") && lastElement.equals("Hydro") && 
                   System.currentTimeMillis() - lastCastTime < 1000) {
            // Cast đồng thời (trong vòng 1 giây)
            reaction = ReactionType.BOILING_WATER;
        }
        
        if (reaction != null) {
            triggerReaction(player, reaction, targetLocation, lastSpell, spellName);
        }
        
        // Cập nhật spell gần nhất
        data.setLastCastedSpell(spellName);
        data.setLastCastTime(System.currentTimeMillis());
    }
      /**
     * Kích hoạt phản ứng nguyên tố
     */
    private static void triggerReaction(Player player, ReactionType reaction, Location location, 
                                      String firstSpell, String secondSpell) {
        
        // Thông báo cho người chơi
        String message = HexColor.toLegacy(reaction.getColor(), 
            "⚡ COMBO: " + reaction.getName() + " ⚡");
        player.sendMessage(message);
        
        // Hiệu ứng dựa trên loại phản ứng
        switch (reaction) {
            case VAPORIZE:
                executeVaporize(player, location);
                break;
            case STEAM_CLOUD:
                executeSteamCloud(player, location);
                break;
            case BOILING_WATER:
                executeBoilingWater(player, location);
                break;
        }
          // Thưởng EXP cho combo
        PlayerData.PlayerMagicData data = PlayerData.getInstance().getPlayerData(player);
        data.setExperience(data.getExperience() + 25); // Bonus EXP
        
        player.sendMessage(HexColor.toLegacy("#4caf50", "+ 25 EXP (Combo Bonus)"));
    }
    
    /**
     * Lấy nguyên tố của spell
     */
    private static String getSpellElement(String spellName) {
        if (spellName == null) return "None";
        
        switch (spellName) {
            case "FlameLance": 
            case "Fireball": 
            case "Incinerate":
                return "Pyro";
            case "WaterBolt": 
            case "HydroBlast": 
            case "AquaShield":
                return "Hydro";
            case "WindBlade": 
            case "AirSlash": 
            case "Gust":
                return "Anemo";
            default: 
                return "None";
        }
    }
      /**
     * VAPORIZE: Tăng sát thương Pyro
     */
    private static void executeVaporize(Player player, Location location) {
        PlayerData.PlayerMagicData data = PlayerData.getInstance().getPlayerData(player);
        
        // Buff Pyro damage 50% trong 10 giây
        data.setPyroDamageBonus(1.5); // 150% = +50%
        data.setPyroBoostEnd(System.currentTimeMillis() + 10000); // 10 giây
        
        // Hiển thị buff notification
        BuffNotificationManager.getInstance().addDamageBoost(player, "Pyro", 50, 200); // 200 ticks = 10 giây
        
        // Áp dụng elemental status cho nearby monsters
        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 5, 3, 5)) {
            if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                MonsterStatusManager.getInstance().applyElementalStatus(
                    (org.bukkit.entity.LivingEntity) entity, "Vaporize", 10, player);
            }
        }
        
        // Visual effects
        location.getWorld().spawnParticle(Particle.FLAME, location, 30, 1, 1, 1, 0.1);
        location.getWorld().spawnParticle(Particle.CLOUD, location, 20, 1, 1, 1, 0.05);
        
        // Sound effect
        location.getWorld().playSound(location, Sound.BLOCK_FIRE_EXTINGUISH, 1.0f, 1.5f);
        
        player.sendMessage(HexColor.toLegacy("#ff4500", "🔥 Pyro Boost: +50% sát thương trong 10 giây!"));
    }
      /**
     * STEAM CLOUD: Tạo đám mây hơi che tầm nhìn
     */
    private static void executeSteamCloud(Player player, Location location) {
        // Hiển thị reaction notification
        BuffNotificationManager.getInstance().addReactionNotification(player, "Steam Cloud", 100); // 5 giây
        
        // Tạo steam cloud trong 5 giây
        for (int i = 0; i < 100; i++) { // 100 ticks = 5 giây
            final int tick = i;
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Tạo dense cloud particles
                    for (int j = 0; j < 10; j++) {
                        double x = (Math.random() - 0.5) * 6;
                        double y = Math.random() * 3;
                        double z = (Math.random() - 0.5) * 6;
                        
                        Location cloudLoc = location.clone().add(x, y, z);
                        cloudLoc.getWorld().spawnParticle(Particle.CLOUD, cloudLoc, 5, 0.5, 0.5, 0.5, 0.02);
                    }
                    
                    // Blindness effect for enemies
                    if (tick % 20 == 0) { // Mỗi giây
                        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 6, 3, 6)) {
                            if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                                org.bukkit.entity.LivingEntity livingEntity = (org.bukkit.entity.LivingEntity) entity;
                                livingEntity.addPotionEffect(new org.bukkit.potion.PotionEffect(
                                    org.bukkit.potion.PotionEffectType.BLINDNESS, 40, 2));
                                
                                // Áp dụng Steam status cho monster
                                MonsterStatusManager.getInstance().applyElementalStatus(
                                    livingEntity, "Steam", 5, player);
                            }
                        }
                    }
                }
            }.runTaskLater(Bukkit.getPluginManager().getPlugin("Magic"), tick);
        }
        
        // Sound effect
        location.getWorld().playSound(location, Sound.BLOCK_FIRE_EXTINGUISH, 1.5f, 0.8f);
        
        player.sendMessage(HexColor.toLegacy("#87ceeb", "💨 Steam Cloud: Che tầm nhìn kẻ thù trong 5 giây!"));
    }
      /**
     * BOILING WATER: Nước sôi gây damage liên tục
     */
    private static void executeBoilingWater(Player player, Location location) {
        // Hiển thị reaction notification
        BuffNotificationManager.getInstance().addReactionNotification(player, "Boiling Water", 200); // 10 giây
        
        // Tạo vùng nước sôi trong 10 giây
        for (int i = 0; i < 200; i++) { // 200 ticks = 10 giây
            final int tick = i;
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Tạo boiling effect
                    for (int j = 0; j < 8; j++) {
                        double angle = (j * 45) + (tick * 5); // Rotating pattern
                        double radius = 2 + Math.sin(tick * 0.1) * 0.5;
                        double x = radius * Math.cos(Math.toRadians(angle));
                        double z = radius * Math.sin(Math.toRadians(angle));
                        
                        Location boilLoc = location.clone().add(x, 0.1, z);
                        boilLoc.getWorld().spawnParticle(Particle.LAVA, boilLoc, 3, 0.2, 0.1, 0.2, 0);
                        boilLoc.getWorld().spawnParticle(Particle.FLAME, boilLoc, 2, 0.1, 0.1, 0.1, 0.02);
                    }
                    
                    // Damage per second
                    if (tick % 20 == 0) { // Mỗi giây
                        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 3, 2, 3)) {
                            if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                                ((org.bukkit.entity.LivingEntity) entity).damage(2.0, player); // 1 heart per second
                                entity.setFireTicks(20); // Burn effect
                                
                                // Áp dụng Boiling status cho monster
                                MonsterStatusManager.getInstance().applyElementalStatus(
                                    (org.bukkit.entity.LivingEntity) entity, "Boiling", 10, player);
                            }
                        }
                    }
                }
            }.runTaskLater(Bukkit.getPluginManager().getPlugin("Magic"), tick);
        }
        
        // Sound effect
        location.getWorld().playSound(location, Sound.BLOCK_LAVA_POP, 1.0f, 1.2f);
        location.getWorld().playSound(location, Sound.BLOCK_FIRE_AMBIENT, 0.8f, 1.5f);
        
        player.sendMessage(HexColor.toLegacy("#ff4500", "♨️ Boiling Water: 1❤/giây trong 10 giây!"));
    }
}

