package shyrcs;

import org.bukkit.plugin.java.JavaPlugin;
import shyrcs.Commands.GiveWand;
import shyrcs.Commands.SelectElemental;
import shyrcs.Commands.ActionBarCommand;
import shyrcs.Commands.DebugWand;
import shyrcs.Commands.TestSpell;
import shyrcs.Commands.ManaCommand;
import shyrcs.Commands.ComboCommand;
import shyrcs.Manager.MagicManager;
import shyrcs.Manager.ActionBarManager;
import shyrcs.Player.ProfileCommand;
import shyrcs.Player.PlayerData;
import shyrcs.Player.ProfilePlayer;
import shyrcs.Player.Spellbook;
import shyrcs.Player.SelectSkill;
import shyrcs.Wand.WoodWand;
import shyrcs.Listeners.ActionBarListener;

public class MagicPlugin extends JavaPlugin {

    private static MagicPlugin instance;
    private MagicManager magicManager;
    private PlayerData playerData;
    private ActionBarManager actionBarManager;

    @Override
    public void onEnable() {
        instance = this;

        // Khởi tạo managers
        this.magicManager = new MagicManager();
        this.playerData = PlayerData.getInstance();
        this.actionBarManager = ActionBarManager.getInstance();

        // Đăng ký commands
        registerCommands();

        // Đăng ký events
        registerEvents();

        // Khởi tạo magic system
        magicManager.initializeMagicSystem();

        // Bắt đầu action bar cho tất cả người chơi đang online
        actionBarManager.startForAllPlayers();

        getLogger().info("§6[MagicCore] §aPlugin đã được kích hoạt thành công!");
        getLogger().info("§6[MagicCore] §bHệ thống 10 nguyên tố đã sẵn sàng!");
        getLogger().info("§6[MagicCore] §eAction Bar đã được kích hoạt!");
    }

    @Override
    public void onDisable() {
        // Dừng action bar cho tất cả người chơi
        if (actionBarManager != null) {
            actionBarManager.shutdown();
        }

        // Lưu dữ liệu người chơi
        if (playerData != null) {
            playerData.saveAllPlayerData();
        }

        getLogger().info("§6[MagicCore] §cPlugin đã được tắt!");
    }    private void registerCommands() {
        getCommand("magic").setExecutor(new GiveWand());
        getCommand("profile").setExecutor(new ProfileCommand());
        getCommand("elemental").setExecutor(new SelectElemental());
        getCommand("actionbar").setExecutor(new ActionBarCommand());
        getCommand("debugwand").setExecutor(new DebugWand());
        getCommand("testspell").setExecutor(new TestSpell());
        getCommand("mana").setExecutor(new ManaCommand());
        getCommand("combo").setExecutor(new ComboCommand());
    }

    private void registerEvents() {
        getServer().getPluginManager().registerEvents(new WoodWand(), this);
        getServer().getPluginManager().registerEvents(new ProfilePlayer(), this);
        getServer().getPluginManager().registerEvents(new Spellbook(), this);
        getServer().getPluginManager().registerEvents(new SelectSkill(), this);
        getServer().getPluginManager().registerEvents(new ActionBarListener(), this);
    }

    public static MagicPlugin getInstance() {
        return instance;
    }

    public MagicManager getMagicManager() {
        return magicManager;
    }

    public PlayerData getPlayerData() {
        return playerData;
    }
}