package shyrcs.Utils;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.TextColor;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;

public class HexColor {
    
    // <PERSON><PERSON>u của các nguyên tố
    public static final String PYRO = "#ec4343";
    public static final String HYDRO = "#4ec5ff";
    public static final String ANEMO = "#6ed9c7";
    public static final String ELECTRO = "#b47ded";
    public static final String DENDRO = "#82c342";
    public static final String CYRO = "#80c5ff";
    public static final String GEO = "#ffa726";
    public static final String LIGHT = "#ffe066";
    public static final String DARK = "#6b4e7d";
    
    // Màu cho cấp độ phép
    public static final String NOVICE = "#b8ffc6";     // Lục nhạt
    public static final String APPRENTICE = "#4ec5ff"; // Xanh biển
    public static final String ADEPT = "#ffeb3b";      // Vàng
    public static final String EXPERT = "#9c27b0";     // Tím
    public static final String MASTER = "#ff5722";     // Đỏ nhạt
    public static final String LEGENDARY = "#ff9800";  // Vàng đậm
    public static final String MYTHIC = "#e91e63";     // Hồng tím
    
    // Màu cho thông tin
    public static final String INFO = "#e6f2f0";
    public static final String MANA = "#3766f7";
    public static final String COOLDOWN = "#c9abed";
    public static final String RUNE = "#972c46";
    public static final String CHANT = "#bdeeee";
    public static final String DESC = "#f0edcc";
    
    /**
     * Chuyển đổi hex color thành Component
     */
    public static Component colorize(String hexColor, String text) {
        return Component.text(text).color(TextColor.fromHexString(hexColor));
    }
    
    /**
     * Chuyển đổi hex color thành legacy color code
     */
    public static String toLegacy(String hexColor, String text) {
        Component component = Component.text(text).color(TextColor.fromHexString(hexColor));
        return LegacyComponentSerializer.legacySection().serialize(component);
    }
    
    /**
     * Lấy màu theo nguyên tố
     */
    public static String getElementColor(String element) {
        switch (element.toUpperCase()) {
            case "PYRO": return PYRO;
            case "HYDRO": return HYDRO;
            case "ANEMO": return ANEMO;
            case "ELECTRO": return ELECTRO;
            case "DENDRO": return DENDRO;
            case "CYRO": return CYRO;
            case "GEO": return GEO;
            case "LIGHT": return LIGHT;
            case "DARK": return DARK;
            default: return "#ffffff";
        }
    }
    
    /**
     * Lấy màu theo cấp độ
     */
    public static String getRankColor(String rank) {
        switch (rank.toUpperCase()) {
            case "NOVICE": return NOVICE;
            case "APPRENTICE": return APPRENTICE;
            case "ADEPT": return ADEPT;
            case "EXPERT": return EXPERT;
            case "MASTER": return MASTER;
            case "LEGENDARY": return LEGENDARY;
            case "MYTHIC": return MYTHIC;
            default: return "#ffffff";
        }
    }
}
