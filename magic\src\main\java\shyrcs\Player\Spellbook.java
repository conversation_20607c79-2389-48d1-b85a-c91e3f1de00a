package shyrcs.Player;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import shyrcs.Utils.HexColor;
import shyrcs.Manager.ActionBarManager;

import java.util.Arrays;

@SuppressWarnings("deprecation")
public class Spellbook implements Listener {

    /**
     * Mở GUI Spellbook cho người chơi
     */
    public void openSpellbook(Player player) {
        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        String element = data.getSelectedElement();
        String elementColor = HexColor.getElementColor(element);

        Inventory gui = Bukkit.createInventory(null, 36,
            HexColor.toLegacy(elementColor, element) + " | " + HexColor.toLegacy("#ffeb3b", "Cấp 1"));

        // Hiển thị phép đã học
        displayLearnedSpells(gui, data);

        // Nút chuyển trang (slot 27, 35)
        createNavigationButtons(gui);

        // Nút lọc theo cấp độ (slot 28-34)
        createRankFilterButtons(gui);

        player.openInventory(gui);
    }

    /**
     * Hiển thị phép đã học
     */
    private void displayLearnedSpells(Inventory gui, PlayerData.PlayerMagicData data) {
        // Slot 0-26 cho phép thuật
        int slot = 0;

        for (String spellName : data.getLearnedSpells()) {
            if (slot >= 27) break; // Chỉ hiển thị tối đa 27 phép

            ItemStack spellItem = createSpellItem(spellName, data.getSelectedSkill().equals(spellName));
            gui.setItem(slot, spellItem);
            slot++;
        }
    }

    /**
     * Tạo item cho phép thuật
     */
    private ItemStack createSpellItem(String spellName, boolean isSelected) {
        ItemStack item = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta meta = item.getItemMeta();

        String displayName;
        if (isSelected) {
            displayName = HexColor.toLegacy("#4caf50", "✓ " + spellName + " ✓");
        } else {
            displayName = HexColor.toLegacy(HexColor.PYRO, spellName);
        }

        meta.setDisplayName(displayName);
        meta.setLore(Arrays.asList(
            "",
            HexColor.toLegacy(HexColor.INFO, "Mô tả: ") + HexColor.toLegacy(HexColor.DESC, getSpellDescription(spellName)),
            HexColor.toLegacy(HexColor.INFO, "Mana: ") + HexColor.toLegacy(HexColor.MANA, getSpellMana(spellName)),
            HexColor.toLegacy(HexColor.INFO, "Hồi chiêu: ") + HexColor.toLegacy(HexColor.COOLDOWN, getSpellCooldown(spellName)),
            HexColor.toLegacy(HexColor.INFO, "Velkath Rune: ") + HexColor.toLegacy(HexColor.RUNE, getSpellRune(spellName)),
            HexColor.toLegacy(HexColor.INFO, "Niệm Chú: ") + HexColor.toLegacy(HexColor.CHANT, getSpellChant(spellName)),
            "",
            isSelected ?
                HexColor.toLegacy("#4caf50", "▶ Đang được chọn") :
                HexColor.toLegacy("#ffeb3b", "▶ Click để chọn phép này")
        ));

        item.setItemMeta(meta);
        return item;
    }

    /**
     * Tạo nút điều hướng
     */
    private void createNavigationButtons(Inventory gui) {
        // Nút trang trước (slot 27)
        ItemStack prevPage = new ItemStack(Material.ARROW);
        ItemMeta prevMeta = prevPage.getItemMeta();
        prevMeta.setDisplayName(HexColor.toLegacy("#4fc3f7", "◀ Trang Trước"));
        prevPage.setItemMeta(prevMeta);
        gui.setItem(27, prevPage);

        // Nút trang sau (slot 35)
        ItemStack nextPage = new ItemStack(Material.ARROW);
        ItemMeta nextMeta = nextPage.getItemMeta();
        nextMeta.setDisplayName(HexColor.toLegacy("#4fc3f7", "Trang Sau ▶"));
        nextPage.setItemMeta(nextMeta);
        gui.setItem(35, nextPage);
    }

    /**
     * Tạo nút lọc theo cấp độ
     */
    private void createRankFilterButtons(Inventory gui) {
        // Slot 28-34: Các cấp độ
        Material[] materials = {
            Material.WOODEN_SWORD,  // Novice
            Material.BOOK,          // Apprentice
            Material.IRON_SWORD,    // Adept
            Material.BLAZE_ROD,     // Expert
            Material.NETHERITE_SWORD, // Master
            Material.NETHER_STAR,   // Legendary
            Material.DRAGON_EGG     // Mythic
        };

        String[] ranks = {"Novice", "Apprentice", "Adept", "Expert", "Master", "Legendary", "Mythic"};
        String[] colors = {HexColor.NOVICE, HexColor.APPRENTICE, HexColor.ADEPT, HexColor.EXPERT,
                          HexColor.MASTER, HexColor.LEGENDARY, HexColor.MYTHIC};
        String[] descriptions = {
            "Phép nhập môn", "Đang học nghề", "Đã thuần thục phép cơ bản",
            "Hiểu sâu ma thuật", "Pháp sư tinh thông", "Hiếm có, truyền thuyết",
            "Bậc huyền thoại, vượt qua thực tại"
        };

        for (int i = 0; i < ranks.length; i++) {
            ItemStack rankItem = new ItemStack(materials[i]);
            ItemMeta rankMeta = rankItem.getItemMeta();

            rankMeta.setDisplayName(HexColor.toLegacy(colors[i], ranks[i]));
            rankMeta.setLore(Arrays.asList(
                "",
                HexColor.toLegacy("#607d8b", descriptions[i]),
                HexColor.toLegacy("#4fc3f7", "Click để lọc phép cấp " + ranks[i]),
                ""
            ));

            rankItem.setItemMeta(rankMeta);
            gui.setItem(28 + i, rankItem);
        }
    }

    /**
     * Lấy mô tả phép thuật
     */
    private String getSpellDescription(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return "Triệu hồi ngọn thương lửa bay thẳng về phía trước";
            case "WaterBolt":
                return "Bắn một viên đạn nước về phía trước";
            case "WindBlade":
                return "Tạo ra lưỡi gió sắc bén cắt qua kẻ địch";
            default:
                return "Phép thuật bí ẩn";
        }
    }

    /**
     * Lấy mana cost của phép
     */
    private String getSpellMana(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return "20";
            case "WaterBolt":
                return "15";
            case "WindBlade":
                return "18";
            default:
                return "??";
        }
    }

    /**
     * Lấy cooldown của phép
     */
    private String getSpellCooldown(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return "5s";
            case "WaterBolt":
                return "3s";
            case "WindBlade":
                return "4s";
            default:
                return "??";
        }
    }

    /**
     * Lấy rune của phép
     */
    private String getSpellRune(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return "ᚠᛚᚨᛗᛖ ᛚᚨᚾᚲᛖ";
            case "WaterBolt":
                return "ᚹᚨᛏᛖᚱ ᛒᛟᛚᛏ";
            case "WindBlade":
                return "ᚹᛁᚾᛞ ᛒᛚᚨᛞᛖ";
            default:
                return "???";
        }
    }

    /**
     * Lấy niệm chú của phép
     */
    private String getSpellChant(String spellName) {
        System.out.println("DEBUG - getSpellChant called with: '" + spellName + "'");
        System.out.println("DEBUG - spellName length: " + (spellName != null ? spellName.length() : "null"));

        if (spellName != null) {
            // Debug từng ký tự
            for (int i = 0; i < spellName.length(); i++) {
                char c = spellName.charAt(i);
                System.out.println("DEBUG - char[" + i + "]: '" + c + "' (code: " + (int)c + ")");
            }
        }

        if (spellName == null) {
            System.out.println("DEBUG - spellName is null");
            return "???";
        }

        switch (spellName) {
            case "FlameLance":
                System.out.println("DEBUG - Matched FlameLance");
                return "flahmeh lahnkeh";
            case "WaterBolt":
                System.out.println("DEBUG - Matched WaterBolt");
                return "wahtehr bohlt";
            case "WindBlade":
                System.out.println("DEBUG - Matched WindBlade");
                return "wihnd blahdeh";
            default:
                System.out.println("DEBUG - No match found for: '" + spellName + "'");
                return "???";
        }
    }

    /**
     * Xử lý click trong Spellbook
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        String title = event.getView().getTitle();
        System.out.println("DEBUG - Inventory title: '" + title + "'");

        // Kiểm tra có phải Spellbook không (title có format "Element | Cấp X")
        if (!title.contains("|")) {
            System.out.println("DEBUG - Not a spellbook inventory (no | found)");
            return;
        }

        event.setCancelled(true);

        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();

        int slot = event.getSlot();
        ItemStack item = event.getCurrentItem();

        System.out.println("DEBUG - Clicked slot: " + slot + ", Item: " + (item != null ? item.getType() : "null"));

        if (item == null || item.getType() == Material.AIR) return;

        // Click vào phép thuật (slot 0-26)
        if (slot >= 0 && slot <= 26) {
            if (item.getType() == Material.ENCHANTED_BOOK) {
                System.out.println("DEBUG - Selecting spell from enchanted book");
                selectSpell(player, item);
            } else {
                System.out.println("DEBUG - Item is not an enchanted book: " + item.getType());
            }
        }

        // Click vào nút điều hướng
        if (slot == 27 || slot == 35) {
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Tính năng phân trang sẽ được cập nhật!"));
        }

        // Click vào nút lọc cấp độ (slot 28-34)
        if (slot >= 28 && slot <= 34) {
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Tính năng lọc theo cấp độ sẽ được cập nhật!"));
        }
    }

    /**
     * Chọn phép thuật
     */
    private void selectSpell(Player player, ItemStack spellItem) {
        String spellName = extractSpellName(spellItem.getItemMeta().getDisplayName());

        System.out.println("DEBUG - selectSpell called with spell: '" + spellName + "'");

        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);

        data.setSelectedSkill(spellName);

        System.out.println("DEBUG - Set selected skill to: '" + spellName + "'");
        System.out.println("DEBUG - Getting chant for: '" + spellName + "' = '" + getSpellChant(spellName) + "'");

        // Cập nhật lore đũa phép
        updateWandLore(player, spellName);

        // Cập nhật action bar ngay lập tức
        ActionBarManager.getInstance().updateImmediately(player);

        player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã chọn phép: " + spellName));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "Niệm chú: " + getSpellChant(spellName)));

        // Đóng inventory thay vì refresh để tránh loop
        player.closeInventory();

        System.out.println("DEBUG - selectSpell completed");
    }

    /**
     * Trích xuất tên phép từ display name
     */
    private String extractSpellName(String displayName) {
        // Loại bỏ tất cả color codes (cả legacy và hex)
        String clean = displayName.replaceAll("§[0-9a-fk-or]", ""); // Legacy colors
        clean = clean.replaceAll("§x(§[0-9A-Fa-f]){6}", ""); // Hex colors
        clean = clean.replaceAll("§.", ""); // Bất kỳ color code nào khác

        // Loại bỏ tất cả ký tự ✓ và khoảng trắng thừa
        clean = clean.replaceAll("✓", "");
        clean = clean.replaceAll("\\s+", " "); // Thay nhiều khoảng trắng thành 1
        clean = clean.trim();

        // Debug
        System.out.println("DEBUG - Original display name: '" + displayName + "'");
        System.out.println("DEBUG - After all cleaning: '" + clean + "'");

        return clean;
    }

    /**
     * Cập nhật lore của đũa phép sau khi chọn skill
     */
    private void updateWandLore(Player player, String selectedSpell) {
        ItemStack wandItem = player.getInventory().getItemInMainHand();

        // Kiểm tra có phải đũa phép không
        if (wandItem == null || wandItem.getItemMeta() == null) return;

        String displayName = wandItem.getItemMeta().getDisplayName();
        if (displayName == null || !displayName.contains("Đũa Phép")) return;

        // Lấy lore hiện tại
        ItemMeta meta = wandItem.getItemMeta();
        java.util.List<String> lore = meta.getLore();

        if (lore != null) {
            // Tìm và cập nhật dòng "Select Skill"
            for (int i = 0; i < lore.size(); i++) {
                String line = lore.get(i);
                String cleanLine = line.replaceAll("§[0-9a-fk-or]", "");

                if (cleanLine.contains("Select Skill:")) {
                    // Cập nhật dòng này với skill mới
                    String newLine = "<color:#4ecdc4>Select Skill: </color><color:#4caf50>" + selectedSpell + "</color>";
                    lore.set(i, newLine);
                    break;
                }
            }

            // Áp dụng lore mới
            meta.setLore(lore);
            wandItem.setItemMeta(meta);

            System.out.println("DEBUG - Updated wand lore with selected spell: " + selectedSpell);
        }
    }
}
