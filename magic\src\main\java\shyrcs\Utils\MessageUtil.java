package shyrcs.Utils;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.minimessage.MiniMessage;
import net.kyori.adventure.text.minimessage.tag.resolver.Placeholder;
import net.kyori.adventure.text.minimessage.tag.resolver.TagResolver;
import org.bukkit.entity.Player;

public class MessageUtil {
    
    private static final MiniMessage miniMessage = MiniMessage.miniMessage();
    
    /**
     * Parse MiniMessage string thành Component
     */
    public static Component getComponentParsed(String message, Player player) {
        if (message == null || message.isEmpty()) {
            return Component.empty();
        }
        
        try {
            // Có thể thêm placeholders cho player nếu cần
            if (player != null) {
                TagResolver playerResolver = TagResolver.resolver(
                    Placeholder.unparsed("player", player.getName()),
                    Placeholder.unparsed("player_display", player.getDisplayName())
                );
                return miniMessage.deserialize(message, playerResolver);
            } else {
                return miniMessage.deserialize(message);
            }
        } catch (Exception e) {
            // Fallback nếu có lỗi parsing
            return Component.text(message);
        }
    }
    
    /**
     * Parse MiniMessage string thành Component (không cần player)
     */
    public static Component getComponentParsed(String message) {
        return getComponentParsed(message, null);
    }
    
    /**
     * Tạo Component với màu sắc đơn giản
     */
    public static Component colored(String color, String text) {
        return getComponentParsed("<" + color + ">" + text + "</" + color + ">");
    }
    
    /**
     * Tạo Component với hex color
     */
    public static Component hex(String hexColor, String text) {
        return getComponentParsed("<color:" + hexColor + ">" + text + "</color>");
    }
    
    /**
     * Tạo Component với gradient
     */
    public static Component gradient(String color1, String color2, String text) {
        return getComponentParsed("<gradient:" + color1 + ":" + color2 + ">" + text + "</gradient>");
    }
}
