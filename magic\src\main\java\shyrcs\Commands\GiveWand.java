package shyrcs.Commands;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import shyrcs.Utils.HexColor;
import shyrcs.Utils.ItemBuilder;

public class GiveWand implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // /magic givewand <player> <wandType>

        if (args.length < 3) {
            sender.sendMessage(HexColor.toLegacy("#ff6b6b", "Sử dụng: /magic givewand <player> <wandType>"));
            return true;
        }

        // args[0] = "givewand", args[1] = player, args[2] = wandType
        String playerName = args[1];
        String wandType = args[2];
        String signature = playerName; // Player name ch<PERSON>h là signature

        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(HexColor.toLegacy("#ff6b6b", "Không tìm thấy người chơi: " + playerName));
            return true;
        }

        ItemStack wand = createWand(wandType, signature);
        if (wand == null) {
            sender.sendMessage(HexColor.toLegacy("#ff6b6b", "Loại đũa không hợp lệ: " + wandType));
            return true;
        }

        target.getInventory().addItem(wand);

        sender.sendMessage(HexColor.toLegacy("#4ecdc4", "Đã tặng đũa phép " + wandType + " cho " + playerName + " (Signature: " + signature + ")"));
        target.sendMessage(HexColor.toLegacy("#4ecdc4", "Bạn đã nhận được đũa phép " + wandType + "! Chỉ bạn mới có thể sử dụng."));

        return true;
    }

    private ItemStack createWand(String wandType, String signature) {
        Material material;
        String displayName;

        switch (wandType.toLowerCase()) {
            case "wood":
            case "wooden":
                material = Material.STICK;
                displayName = HexColor.toLegacy("#8b4513", "Đũa Phép Gỗ");
                break;
            case "iron":
                material = Material.IRON_SWORD;
                displayName = HexColor.toLegacy("#c0c0c0", "Đũa Phép Sắt");
                break;
            case "gold":
            case "golden":
                material = Material.GOLDEN_SWORD;
                displayName = HexColor.toLegacy("#ffd700", "Đũa Phép Vàng");
                break;
            case "diamond":
                material = Material.DIAMOND_SWORD;
                displayName = HexColor.toLegacy("#b9f2ff", "Đũa Phép Kim Cương");
                break;
            case "netherite":
                material = Material.NETHERITE_SWORD;
                displayName = HexColor.toLegacy("#654321", "Đũa Phép Netherite");
                break;
            default:
                return null;
        }

        return new ItemBuilder(material)
            .setDisplayName(displayName)
            .setLore(
                "",
                HexColor.toLegacy(HexColor.INFO, "Select Skill: ") + HexColor.toLegacy("#ffeb3b", "Chưa chọn"),
                "",
                "Signature: " + signature,
                "",
                HexColor.toLegacy("#90ee90", "▶ Chuột phải để mở Spellbook"),
                HexColor.toLegacy("#90ee90", "▶ Chat niệm chú để thi triển phép")
            )
            .setCustomModelData(1000 + wandType.hashCode() % 1000)
            .build();
    }
}
