package shyrcs.Commands;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import shyrcs.Utils.MessageUtil;
import shyrcs.Utils.ItemBuilder;

public class GiveWand implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // /magic givewand <player> <wandType>

        if (args.length < 3) {
            sender.sendMessage(MessageUtil.hex("#ff6b6b", "Sử dụng: /magic givewand <player> <wandType>"));
            return true;
        }

        // args[0] = "givewand", args[1] = player, args[2] = wandType
        String playerName = args[1];
        String wandType = args[2];
        String signature = playerName; // Player name ch<PERSON>h là signature

        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(MessageUtil.hex("#ff6b6b", "Không tìm thấy người chơi: " + playerName));
            return true;
        }

        ItemStack wand = createWand(wandType, signature);
        if (wand == null) {
            sender.sendMessage(MessageUtil.hex("#ff6b6b", "Loại đũa không hợp lệ: " + wandType));
            return true;
        }

        target.getInventory().addItem(wand);

        sender.sendMessage(MessageUtil.hex("#4ecdc4", "Đã tặng đũa phép " + wandType + " cho " + playerName + " (Signature: " + signature + ")"));
        target.sendMessage(MessageUtil.hex("#4ecdc4", "Bạn đã nhận được đũa phép " + wandType + "! Chỉ bạn mới có thể sử dụng."));

        return true;
    }

    private ItemStack createWand(String wandType, String signature) {
        Material material;
        String displayName;        switch (wandType.toLowerCase()) {
            case "wood":
            case "wooden":
                material = Material.STICK;
                displayName = "<!italic><color:#FFFFFF>Đũa Phép</color>";
                break;
            case "iron":
                material = Material.IRON_SWORD;
                displayName = "<!italic><color:#c0c0c0>Đũa Phép Sắt</color>";
                break;
            case "gold":
            case "golden":
                material = Material.GOLDEN_SWORD;
                displayName = "<!italic><color:#ffd700>Đũa Phép Vàng</color>";
                break;
            case "diamond":
                material = Material.DIAMOND_SWORD;
                displayName = "<!italic><color:#b9f2ff>Đũa Phép Kim Cương</color>";
                break;
            case "netherite":
                material = Material.NETHERITE_SWORD;
                displayName = "<!italic><color:#654321>Đũa Phép Netherite</color>";
                break;
            default:
                return null;
        }

        return new ItemBuilder(material)
            .setDisplayName(displayName)
            .setLore(
                "",
                "<!italic><color:#4ecdc4>Select Skill: </color><color:#ffeb3b>Chưa chọn</color>",
                "",
                "<!italic><color:#F86666>Signature: </color><color:#E0E0E0>" + signature + "</color>",
                "",
                "<!italic><color:#90ee90>▶ Chuột phải để mở Spellbook</color>",
                "<!italic><color:#90ee90>▶ Niệm chú để thi triển phép</color>"
            )
            .setCustomModelData(1000 + wandType.hashCode() % 1000)
            .build();
    }
}
