package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.Player.PlayerData;
import shyrcs.Utils.HexColor;

public class <PERSON>aCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);
        
        if (args.length == 0) {
            // Hiển thị thông tin mana hiện tại
            player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== MANA INFO ==="));
            player.sendMessage(HexColor.toLegacy("#4fc3f7", "Mana hiện tại: " + (int)data.getMana() + "/" + (int)data.getMaxMana()));
            player.sendMessage(HexColor.toLegacy("#4caf50", "Hồi phục: 5 mana/giây"));
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Commands:"));
            player.sendMessage(HexColor.toLegacy("#ffffff", "/mana set <amount> - Set mana"));
            player.sendMessage(HexColor.toLegacy("#ffffff", "/mana add <amount> - Thêm mana"));
            player.sendMessage(HexColor.toLegacy("#ffffff", "/mana full - Đầy mana"));
            player.sendMessage(HexColor.toLegacy("#ffffff", "/mana empty - Hết mana"));
            return true;
        }
        
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "set":
                if (args.length < 2) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Usage: /mana set <amount>"));
                    return true;
                }
                
                try {
                    int amount = Integer.parseInt(args[1]);
                    int maxMana = (int) data.getMaxMana();
                    amount = Math.max(0, Math.min(amount, maxMana)); // Clamp 0-maxMana
                    
                    data.setMana(amount);
                    player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã set mana: " + amount + "/" + maxMana));
                } catch (NumberFormatException e) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Số không hợp lệ!"));
                }
                break;
                
            case "add":
                if (args.length < 2) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Usage: /mana add <amount>"));
                    return true;
                }
                
                try {
                    int amount = Integer.parseInt(args[1]);
                    double currentMana = data.getMana();
                    double maxMana = data.getMaxMana();
                    double newMana = Math.min(currentMana + amount, maxMana);
                    
                    data.setMana((int) newMana);
                    player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã thêm " + amount + " mana: " + (int)newMana + "/" + (int)maxMana));
                } catch (NumberFormatException e) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Số không hợp lệ!"));
                }
                break;
                
            case "full":
                data.setMana((int) data.getMaxMana());
                player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Mana đã đầy: " + (int)data.getMaxMana() + "/" + (int)data.getMaxMana()));
                break;
                
            case "empty":
                data.setMana(0);
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "✗ Mana đã hết: 0/" + (int)data.getMaxMana()));
                break;
                
            case "info":
            case "status":
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "Mana: " + (int)data.getMana() + "/" + (int)data.getMaxMana()));
                break;
                
            default:
                player.sendMessage(HexColor.toLegacy("#ffeb3b", "Commands:"));
                player.sendMessage(HexColor.toLegacy("#ffffff", "/mana - Hiển thị info"));
                player.sendMessage(HexColor.toLegacy("#ffffff", "/mana set <amount> - Set mana"));
                player.sendMessage(HexColor.toLegacy("#ffffff", "/mana add <amount> - Thêm mana"));
                player.sendMessage(HexColor.toLegacy("#ffffff", "/mana full - Đầy mana"));
                player.sendMessage(HexColor.toLegacy("#ffffff", "/mana empty - Hết mana"));
                break;
        }
        
        return true;
    }
}
