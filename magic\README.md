# 🔮 MagicCore - <PERSON><PERSON>hống Ma Thuật Phức Tạp

## 📋 Tổng Quan

MagicCore là một plugin Minecraft phức tạp với hệ thống ma thuật 10 nguyên tố, đư<PERSON><PERSON> thiết kế để tạo ra trải nghiệm ma thuật sâu sắc và đa dạng.

## ✨ Tính Năng <PERSON>ính

### 🌟 10 Nguyên <PERSON>ố
- **Pyro** (#ec4343) - L<PERSON>a
- **Hydro** (#4ec5ff) - Nước  
- **Anemo** (#7dd3c0) - Gió
- **Electro** (#b47ded) - Đ<PERSON>ện
- **Dendro** (#82c342) - <PERSON><PERSON><PERSON> cối
- **Cryo** (#80c5ff) - Băng
- **Geo** (#ffa726) - Đ<PERSON><PERSON> đá
- **Light** (#ffe066) - <PERSON><PERSON> sáng
- **Dark** (#6b4e7d) - B<PERSON>g tối
- **Quantum** (#ff6b9d) - <PERSON><PERSON><PERSON><PERSON> tử

### 🎯 7 C<PERSON><PERSON> Đ<PERSON> Thu<PERSON>t
1. **Novice** - <PERSON><PERSON> <PERSON><PERSON> (Lục <PERSON>)
2. **Apprentice** - Học việc (Xanh biển)
3. **Adept** - Thành thạo (Vàng)
4. **Expert** - Chuyên gia (Tím)
5. **Master** - Bậc thầy (Đỏ nhạt)
6. **Legendary** - Huyền thoại (Vàng đậm)
7. **Mythic** - Thần thoại (Hồng tím)

### 🎮 Hệ Thống GUI

#### ProfilePlayer (54 slots)
- **Slot 4**: Tên + cấp độ người chơi
- **Slot 10**: Thuộc tính (HP, Mana, Stamina)
- **Slot 12**: Buffs/Debuffs
- **Slot 14**: Danh sách phép đã học (click mở Spellbook)
- **Slot 16**: Kỹ năng chiến đấu
- **Slot 20**: Kháng nguyên tố
- **Slot 22**: Thống kê tổng quát
- **Slot 24**: Danh hiệu
- **Slot 38-42**: 4 Rune slots
- **Slot 49**: Nút thoát

#### Spellbook (36 slots)
- **Slot 0-26**: Hiển thị phép đã học
- **Slot 27, 35**: Nút chuyển trang
- **Slot 28-34**: Lọc theo cấp độ

## 🎯 Phép Thuật Hiện Có

### Pyro - Novice
**Flame Lance**
- **Mô tả**: Triệu hồi ngọn thương lửa bay thẳng về phía trước
- **Mana**: 20
- **Cooldown**: 5 giây
- **Rune**: ᚠᛚᚨᛗᛖ ᛚᚨᚾᚲᛖ
- **Niệm chú**: `flahmeh lahnkeh`

## 🎮 Cách Sử Dụng

### Commands
- `/magic givewand <player>=<signature> <wandType>` - Tặng đũa phép
- `/profile` - Mở GUI profile
- `/elemental <element>` - Chọn nguyên tố chính

### Đũa Phép
1. **Chuột phải** với đũa phép để mở Spellbook
2. **Chat niệm chú** để thi triển phép (cần cầm đũa phép)

### Thi Triển Phép
1. Cầm đũa phép trong tay
2. Chọn phép trong Spellbook
3. Chat niệm chú tương ứng
4. Phép sẽ được thi triển nếu đủ mana và không trong cooldown

## 🛠️ Cài Đặt

### Yêu Cầu
- Java 17+
- Paper/Spigot 1.21.4+
- Maven 3.6+

### Build
```bash
cd magic
mvn clean package
```

### Cài Đặt Plugin
1. Copy file `.jar` vào folder `plugins/`
2. Restart server
3. Plugin sẽ tự động tạo config

## 📁 Cấu Trúc Code

```
src/main/java/shyrcs/
├── MagicPlugin.java          # Main plugin class
├── Commands/                 # Commands
│   ├── GiveWand.java
│   └── SelectElemental.java
├── Magic/                    # Phép thuật theo nguyên tố
│   ├── Spell.java           # Interface chung
│   ├── Pyro/Novice/FlameLance.java
│   └── [9 nguyên tố khác]/[7 cấp độ]/
├── Manager/                  # Quản lý hệ thống
│   ├── MagicManager.java
│   └── SpellRegistry.java
├── Player/                   # Hệ thống người chơi
│   ├── PlayerData.java
│   ├── ProfilePlayer.java
│   ├── Spellbook.java
│   └── SelectSkill.java
├── Utils/                    # Tiện ích
│   └── HexColor.java
└── Wand/                     # Đũa phép
    └── WoodWand.java
```

## 🔮 Tính Năng Sắp Tới

- [ ] 63 phép thuật cho 9 nguyên tố còn lại
- [ ] Hệ thống Rune
- [ ] Combo phép thuật
- [ ] PvP magic system
- [ ] Magic dungeons
- [ ] Spell crafting
- [ ] Magic items & artifacts

## 🐛 Báo Lỗi

Nếu gặp lỗi, vui lòng báo cáo với thông tin:
- Phiên bản server
- Phiên bản plugin
- Mô tả lỗi chi tiết
- Console log

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.
