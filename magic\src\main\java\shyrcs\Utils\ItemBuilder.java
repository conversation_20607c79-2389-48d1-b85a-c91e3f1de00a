package shyrcs.Utils;

import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;

/**
 * Utility class để tạo ItemStack với Adventure API
 * Tránh sử dụng deprecated methods
 */
public class ItemBuilder {
    
    private final ItemStack item;
    private final ItemMeta meta;
    
    public ItemBuilder(Material material) {
        this.item = new ItemStack(material);
        this.meta = item.getItemMeta();
    }
    
    public ItemBuilder(ItemStack item) {
        this.item = item;
        this.meta = item.getItemMeta();
    }
    
    /**
     * Set display name sử dụng Adventure API
     */
    public ItemBuilder setDisplayName(String legacyText) {
        meta.displayName(HexColor.createComponent(legacyText));
        return this;
    }
    
    /**
     * Set lore sử dụng Adventure API
     */
    public ItemBuilder setLore(String... loreLines) {
        List<Component> loreComponents = Arrays.stream(loreLines)
            .map(HexColor::createComponent)
            .toList();
        meta.lore(loreComponents);
        return this;
    }
    
    /**
     * Set lore từ List<String>
     */
    public ItemBuilder setLore(List<String> loreLines) {
        List<Component> loreComponents = loreLines.stream()
            .map(HexColor::createComponent)
            .toList();
        meta.lore(loreComponents);
        return this;
    }
    
    /**
     * Set custom model data
     */
    public ItemBuilder setCustomModelData(int data) {
        meta.setCustomModelData(data);
        return this;
    }
    
    /**
     * Build ItemStack
     */
    public ItemStack build() {
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * Get ItemMeta để thực hiện thêm modifications
     */
    public ItemMeta getMeta() {
        return meta;
    }
    
    /**
     * Static method để tạo nhanh
     */
    public static ItemStack create(Material material, String displayName, String... lore) {
        return new ItemBuilder(material)
            .setDisplayName(displayName)
            .setLore(lore)
            .build();
    }
    
    /**
     * Static method để tạo nhanh với List lore
     */
    public static ItemStack create(Material material, String displayName, List<String> lore) {
        return new ItemBuilder(material)
            .setDisplayName(displayName)
            .setLore(lore)
            .build();
    }
}
