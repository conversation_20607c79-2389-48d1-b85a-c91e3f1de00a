package shyrcs.Utils;

import net.kyori.adventure.text.Component;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;

public class ItemBuilder {
    
    private final ItemStack item;
    private final ItemMeta meta;
    
    public ItemBuilder(Material material) {
        this.item = new ItemStack(material);
        this.meta = item.getItemMeta();
    }
    
    public ItemBuilder(ItemStack item) {
        this.item = item;
        this.meta = item.getItemMeta();
    }
    

    public ItemBuilder setDisplayName(String legacyText) {
        meta.displayName(HexColor.createComponent(legacyText));
        return this;
    }

    public ItemBuilder setLore(String... loreLines) {
        List<Component> loreComponents = Arrays.stream(loreLines)
            .map(HexColor::createComponent)
            .toList();
        meta.lore(loreComponents);
        return this;
    }

    public ItemBuilder setLore(List<String> loreLines) {
        List<Component> loreComponents = loreLines.stream()
            .map(HexColor::createComponent)
            .toList();
        meta.lore(loreComponents);
        return this;
    }
    
    public ItemBuilder setCustomModelData(int data) {
        meta.setCustomModelData(data);
        return this;
    }
    
    public ItemStack build() {
        item.setItemMeta(meta);
        return item;
    }

    public ItemMeta getMeta() {
        return meta;
    }
    
    public static ItemStack create(Material material, String displayName, String... lore) {
        return new ItemBuilder(material)
            .setDisplayName(displayName)
            .setLore(lore)
            .build();
    }
    
    public static ItemStack create(Material material, String displayName, List<String> lore) {
        return new ItemBuilder(material)
            .setDisplayName(displayName)
            .setLore(lore)
            .build();
    }
}
