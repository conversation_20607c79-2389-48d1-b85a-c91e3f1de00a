package shyrcs.Manager;

import shyrcs.Magic.Spell;
import shyrcs.Magic.Pyro.Novice.FlameLance;

import java.util.*;
import java.util.logging.Logger;

public class MagicManager {

    private final Map<String, List<Spell>> spellsByElement = new HashMap<>();
    private final Map<String, List<Spell>> spellsByRank = new HashMap<>();
    private final Map<String, Spell> spellsByName = new HashMap<>();
    private final Logger logger;

    // Danh sách 10 nguyên tố
    private final String[] ELEMENTS = {
        "Pyro", "Hydro", "Anemo", "<PERSON><PERSON><PERSON>", "<PERSON>dro",
        "Cryo", "Geo", "Light", "Dark", "Quantum"
    };

    // Danh sách 7 cấp độ
    private final String[] RANKS = {
        "Novice", "Apprentice", "Adept", "Expert",
        "Master", "Legendary", "Mythic"
    };

    public MagicManager() {
        this.logger = Logger.getLogger("MagicCore");
    }

    /**
     * Khởi tạo hệ thống ma thuật
     */
    public void initializeMagicSystem() {
        logger.info("§6[MagicManager] §bĐang khởi tạo hệ thống ma thuật...");

        // Khởi tạo maps cho từng nguyên tố và cấp độ
        for (String element : ELEMENTS) {
            spellsByElement.put(element, new ArrayList<>());
        }

        for (String rank : RANKS) {
            spellsByRank.put(rank, new ArrayList<>());
        }

        // Đăng ký các phép thuật
        registerSpells();

        logger.info("§6[MagicManager] §aĐã tải " + spellsByName.size() + " phép thuật!");
        logSpellStatistics();
    }

    /**
     * Đăng ký tất cả phép thuật
     */
    private void registerSpells() {
        // Đăng ký phép Pyro
        registerPyroSpells();

        // TODO: Đăng ký các nguyên tố khác
        // registerHydroSpells();
        // registerAnemoSpells();
        // ... etc
    }

    /**
     * Đăng ký phép thuật Pyro
     */
    private void registerPyroSpells() {
        // Novice spells
        registerSpell(new FlameLance());

        // TODO: Thêm các phép Pyro khác
    }

    /**
     * Đăng ký một phép thuật
     */
    public void registerSpell(Spell spell) {
        String name = spell.getName();
        String element = spell.getElement();
        String rank = spell.getRank();

        // Thêm vào map theo tên
        spellsByName.put(name.toLowerCase(), spell);

        // Thêm vào map theo nguyên tố
        spellsByElement.get(element).add(spell);

        // Thêm vào map theo cấp độ
        spellsByRank.get(rank).add(spell);

        logger.info("§6[MagicManager] §aĐã đăng ký phép: §e" + name + " §7(" + element + " - " + rank + ")");
    }

    /**
     * Lấy phép thuật theo tên
     */
    public Spell getSpellByName(String name) {
        return spellsByName.get(name.toLowerCase());
    }

    /**
     * Lấy danh sách phép theo nguyên tố
     */
    public List<Spell> getSpellsByElement(String element) {
        return spellsByElement.getOrDefault(element, new ArrayList<>());
    }

    /**
     * Lấy danh sách phép theo cấp độ
     */
    public List<Spell> getSpellsByRank(String rank) {
        return spellsByRank.getOrDefault(rank, new ArrayList<>());
    }

    /**
     * Lấy tất cả phép thuật
     */
    public Collection<Spell> getAllSpells() {
        return spellsByName.values();
    }

    /**
     * Kiểm tra phép thuật có tồn tại
     */
    public boolean spellExists(String name) {
        return spellsByName.containsKey(name.toLowerCase());
    }

    /**
     * Lấy danh sách tất cả nguyên tố
     */
    public String[] getElements() {
        return ELEMENTS.clone();
    }

    /**
     * Lấy danh sách tất cả cấp độ
     */
    public String[] getRanks() {
        return RANKS.clone();
    }

    /**
     * In thống kê phép thuật
     */
    private void logSpellStatistics() {
        logger.info("§6[MagicManager] §b=== THỐNG KÊ PHÉP THUẬT ===");

        for (String element : ELEMENTS) {
            int count = spellsByElement.get(element).size();
            if (count > 0) {
                logger.info("§6[MagicManager] §e" + element + ": §a" + count + " phép");
            }
        }

        logger.info("§6[MagicManager] §b========================");
    }
}