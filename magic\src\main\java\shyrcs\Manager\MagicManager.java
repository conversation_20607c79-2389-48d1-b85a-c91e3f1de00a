package shyrcs.Manager;

import shyrcs.Magic.Spell;

import java.util.*;
import java.util.logging.Logger;

public class MagicManager {

    private final SpellRegistry spellRegistry;
    private final Logger logger;

    public MagicManager() {
        this.logger = Logger.getLogger("MagicCore");
        this.spellRegistry = new SpellRegistry();
    }

    /**
     * Khởi tạo hệ thống ma thuật
     */
    public void initializeMagicSystem() {
        logger.info("§6[MagicManager] §bĐang khởi tạo hệ thống ma thuật...");

        // SpellRegistry đã tự động đăng ký các phép thuật
        int spellCount = spellRegistry.getAllSpells().size();
        logger.info("§6[MagicManager] §aĐã tải " + spellCount + " phép thuật!");

        logSpellStatistics();
    }

    /**
     * <PERSON><PERSON><PERSON> phép thuật theo tên
     */
    public Spell getSpellByName(String name) {
        return spellRegistry.getSpell(name);
    }

    /**
     * <PERSON><PERSON>y danh sách phép theo nguyên tố
     */
    public List<Spell> getSpellsByElement(String element) {
        return spellRegistry.getSpellsByElement(element);
    }

    /**
     * Lấy danh sách phép theo cấp độ
     */
    public List<Spell> getSpellsByRank(String rank) {
        return spellRegistry.getSpellsByRank(rank);
    }

    /**
     * Lấy tất cả phép thuật
     */
    public Collection<Spell> getAllSpells() {
        return spellRegistry.getAllSpells();
    }

    /**
     * Kiểm tra phép thuật có tồn tại
     */
    public boolean spellExists(String name) {
        return spellRegistry.hasSpell(name);
    }

    /**
     * Lấy danh sách tất cả nguyên tố
     */
    public String[] getElements() {
        return SpellRegistry.ELEMENTS.clone();
    }

    /**
     * Lấy danh sách tất cả cấp độ
     */
    public String[] getRanks() {
        return SpellRegistry.RANKS.clone();
    }

    /**
     * Lấy SpellRegistry
     */
    public SpellRegistry getSpellRegistry() {
        return spellRegistry;
    }

    /**
     * In thống kê phép thuật
     */
    private void logSpellStatistics() {
        logger.info("§6[MagicManager] §b=== THỐNG KÊ PHÉP THUẬT ===");

        for (String element : SpellRegistry.ELEMENTS) {
            int count = spellRegistry.getSpellsByElement(element).size();
            if (count > 0) {
                logger.info("§6[MagicManager] §e" + element + ": §a" + count + " phép");
            }
        }

        logger.info("§6[MagicManager] §b========================");
    }
}