#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Velkath Rune Converter Tool
Converts Latin text to Velkath Runes and pronunciation
"""

class VelkathConverter:
    def __init__(self):
        # Mapping from Latin to Velkath Rune and pronunciation
        self.conversion_map = {
            'A': {'rune': 'ᚨ', 'name': 'Ar', 'pronunciation': 'ah'},
            'B': {'rune': 'ᛒ', 'name': 'Brak', 'pronunciation': 'br'},
            'C': {'rune': 'ᚲ', 'name': 'Kez', 'pronunciation': 'k'},
            'D': {'rune': 'ᛞ', 'name': 'Dul', 'pronunciation': 'du'},
            'E': {'rune': 'ᛖ', 'name': 'El', 'pronunciation': 'eh'},
            'F': {'rune': 'ᚠ', 'name': 'Fen', 'pronunciation': 'f'},
            'G': {'rune': 'ᚷ', 'name': '<PERSON><PERSON>', 'pronunciation': 'g'},
            'H': {'rune': 'ᚺ', 'name': 'Hath', 'pronunciation': 'h'},
            'I': {'rune': 'ᛁ', 'name': 'Is', 'pronunciation': 'ee'},
            'J': {'rune': 'ᛃ', 'name': 'Jor', 'pronunciation': 'j'},
            'K': {'rune': 'ᚲ', 'name': 'Kez', 'pronunciation': 'k'},
            'L': {'rune': 'ᛚ', 'name': 'Lur', 'pronunciation': 'l'},
            'M': {'rune': 'ᛗ', 'name': 'Mor', 'pronunciation': 'm'},
            'N': {'rune': 'ᚾ', 'name': 'Nek', 'pronunciation': 'n'},
            'O': {'rune': 'ᛟ', 'name': 'Or', 'pronunciation': 'o'},
            'P': {'rune': 'ᛈ', 'name': 'Par', 'pronunciation': 'p'},
            'Q': {'rune': 'ᛩ', 'name': 'Quen', 'pronunciation': 'kw'},
            'R': {'rune': 'ᚱ', 'name': 'Rah', 'pronunciation': 'r'},
            'S': {'rune': 'ᛋ', 'name': 'Sar', 'pronunciation': 's'},
            'T': {'rune': 'ᛏ', 'name': 'Tor', 'pronunciation': 't'},
            'U': {'rune': 'ᚢ', 'name': 'Ul', 'pronunciation': 'oo'},
            'V': {'rune': 'ᚡ', 'name': 'Val', 'pronunciation': 'v'},
            'W': {'rune': 'ᚥ', 'name': 'Wen', 'pronunciation': 'w'},
            'X': {'rune': 'ᛪ', 'name': 'Xar', 'pronunciation': 'ks'},
            'Y': {'rune': 'ᛦ', 'name': 'Yil', 'pronunciation': 'ai'},
            'Z': {'rune': 'ᛉ', 'name': 'Zek', 'pronunciation': 'z'}
        }
    
    def convert_to_runes(self, text):
        """Convert Latin text to Velkath Runes"""
        result = ""
        for char in text.upper():
            if char in self.conversion_map:
                result += self.conversion_map[char]['rune']
            elif char.isspace():
                result += " "
            else:
                result += char  # Keep punctuation and other characters as is
        return result
    
    def convert_to_pronunciation(self, text):
        """Convert Latin text to Velkath pronunciation"""
        result = ""
        for char in text.upper():
            if char in self.conversion_map:
                result += self.conversion_map[char]['pronunciation']
            elif char.isspace():
                result += " "
            else:
                result += char  # Keep punctuation and other characters as is
        return result
    
    def convert_to_rune_names(self, text):
        """Convert Latin text to Velkath Rune names"""
        result = []
        for char in text.upper():
            if char in self.conversion_map:
                result.append(self.conversion_map[char]['name'])
            elif char.isspace():
                result.append(" ")
            else:
                result.append(char)
        return result
    
    def convert_full(self, text):
        """Convert text to all formats"""
        runes = self.convert_to_runes(text)
        pronunciation = self.convert_to_pronunciation(text)
        rune_names = self.convert_to_rune_names(text)
        
        return {
            'original': text,
            'runes': runes,
            'pronunciation': pronunciation,
            'rune_names': rune_names
        }

def main():
    """Main function for command line interface"""
    import sys
    
    converter = VelkathConverter()
    
    # Check if text is provided as command line argument
    if len(sys.argv) > 1:
        text = " ".join(sys.argv[1:])
        result = converter.convert_full(text)
        
        print(f"Gốc:     {result['original']}")
        print(f"Runes:   {result['runes']}")
        print(f"Phép Chú:  {result['pronunciation']}")
        return
    
    # Interactive mode if no arguments
    print("=== Velkath Rune Converter ===")
    print("Sử dụng: python velkath_converter.py \"text cần chuyển đổi\"")
    print("Hoặc chạy interactive mode\n")
    
    while True:
        text = input("Nhập text (hoặc 'quit' để thoát): ").strip()
        
        if text.lower() in ['quit', 'exit', 'q']:
            break
        
        if not text:
            continue
        
        result = converter.convert_full(text)
        print(f"Runes:   {result['runes']}")
        print(f"Âm đọc:  {result['pronunciation']}")
        print()

def convert_text(text):
    """Helper function for programmatic use"""
    converter = VelkathConverter()
    return converter.convert_full(text)

if __name__ == "__main__":
    main()
