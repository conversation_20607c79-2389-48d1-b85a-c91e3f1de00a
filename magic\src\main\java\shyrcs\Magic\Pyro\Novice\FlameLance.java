package shyrcs.Magic.Pyro.Novice;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Fireball;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.Magic.Spell;

public class FlameLance implements Spell, Listener {

    @Override
    public String getName() {
        return "FlameLance";
    }    @Override
    public String getDescription() {
        return "Triệu hồi ngọn thương lửa bay thẳng về phía trước, gây sát thương nổ cho mục tiêu";
    }

    @Override
    public String getElement() {
        return "Pyro";
    }

    @Override
    public String getRank() {
        return "Novice";
    }

    @Override
    public int getManaCost() {
        return 20;
    }

    @Override
    public long getCooldown() {
        return 5000; // 5 giây
    }

    @Override
    public String getVelkathRune() {
        return "ᚠᛚᚨᛗᛖ ᛚᚨᚾᚲᛖ";
    }

    @Override
    public String getChant() {
        return "flahmeh lahnkeh";
    }

    @Override
    public boolean canCast(Player caster) {
        // Kiểm tra điều kiện cơ bản - có thể mở rộng thêm
        return caster != null && caster.isOnline() && !caster.isDead();
    }

    @Override
    public boolean cast(Player caster) {
        try {
            // Tạo hiệu ứng âm thanh
            caster.getWorld().playSound(caster.getLocation(), Sound.ENTITY_BLAZE_SHOOT, 1.0f, 1.0f);            // Tạo fireball
            Location eyeLocation = caster.getEyeLocation();
            Fireball fireball = caster.getWorld().spawn(eyeLocation, Fireball.class);
            fireball.setShooter(caster);
            fireball.setDirection(eyeLocation.getDirection());
            fireball.setYield(2.0f); // Sức mạnh nổ
            fireball.setIsIncendiary(false); // Không gây cháy

            // Tạo hiệu ứng particle trail
            createParticleTrail(fireball);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Tạo hiệu ứng particle trail cho fireball
     */
    private void createParticleTrail(Fireball fireball) {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (fireball.isDead() || !fireball.isValid()) {
                    this.cancel();
                    return;
                }

                Location loc = fireball.getLocation();

                // Hiệu ứng lửa
                loc.getWorld().spawnParticle(Particle.FLAME, loc, 5, 0.1, 0.1, 0.1, 0.02);

                // Hiệu ứng lava drip
                loc.getWorld().spawnParticle(Particle.DRIPPING_LAVA, loc, 2, 0.1, 0.1, 0.1, 0);

                // Hiệu ứng khói
                loc.getWorld().spawnParticle(Particle.SMOKE, loc, 3, 0.1, 0.1, 0.1, 0.01);
            }
        }.runTaskTimer(Bukkit.getPluginManager().getPlugin("Magic"), 0L, 1L);
    }

    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        if (!(event.getEntity() instanceof Fireball)) return;

        Fireball fireball = (Fireball) event.getEntity();
        if (!(fireball.getShooter() instanceof Player)) return;

        Location hitLocation = fireball.getLocation();

        // Hiệu ứng nổ
        hitLocation.getWorld().playSound(hitLocation, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.0f);

        // Hiệu ứng particle nổ
        hitLocation.getWorld().spawnParticle(Particle.EXPLOSION, hitLocation, 1);
        hitLocation.getWorld().spawnParticle(Particle.FLAME, hitLocation, 20, 1.0, 1.0, 1.0, 0.1);
        hitLocation.getWorld().spawnParticle(Particle.SMOKE, hitLocation, 10, 1.0, 1.0, 1.0, 0.1);
    }
}