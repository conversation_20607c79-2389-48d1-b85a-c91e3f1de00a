package shyrcs.Listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityRegainHealthEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.inventory.ItemStack;
import shyrcs.Manager.ActionBarManager;

/**
 * Listener để quản lý action bar tự động
 */
public class ActionBarListener implements Listener {
    
    private final ActionBarManager actionBarManager;
    
    public ActionBarListener() {
        this.actionBarManager = ActionBarManager.getInstance();
    }
    
    /**
     * Bắt đầu action bar khi player join
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Delay một chút để đảm bảo player đã load hoàn toàn
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 
            () -> actionBarManager.startActionBar(player), 
            20L // 1 giây
        );
    }
    
    /**
     * Dừng action bar khi player quit
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        actionBarManager.stopActionBar(event.getPlayer());
    }
    
    /**
     * Cập nhật action bar khi HP thay đổi
     */
    @EventHandler
    public void onPlayerDamage(EntityDamageEvent event) {
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            
            // Cập nhật sau khi damage được apply
            org.bukkit.Bukkit.getScheduler().runTaskLater(
                org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 
                () -> actionBarManager.updateImmediately(player), 
                1L
            );
        }
    }
    
    /**
     * Cập nhật action bar khi HP hồi phục
     */
    @EventHandler
    public void onPlayerHeal(EntityRegainHealthEvent event) {
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            
            // Cập nhật sau khi heal được apply
            org.bukkit.Bukkit.getScheduler().runTaskLater(
                org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 
                () -> actionBarManager.updateImmediately(player), 
                1L
            );
        }
    }
    
    /**
     * Cập nhật action bar khi player thay đổi item trong tay
     * (để kiểm tra có đang cầm đũa phép không)
     */
    @EventHandler
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        
        // Cập nhật sau khi item được thay đổi
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 
            () -> {
                // Kiểm tra có đang cầm đũa phép không
                ItemStack item = player.getInventory().getItemInMainHand();
                boolean holdingWand = isWand(item);
                
                if (holdingWand) {
                    // Nếu đang cầm đũa phép, đảm bảo action bar đang chạy
                    if (!actionBarManager.isActive(player)) {
                        actionBarManager.startActionBar(player);
                    } else {
                        actionBarManager.updateImmediately(player);
                    }
                }
            }, 
            1L
        );
    }
    
    /**
     * Kiểm tra có phải đũa phép không
     */
    @SuppressWarnings("deprecation")
    private boolean isWand(ItemStack item) {
        if (item == null || item.getItemMeta() == null) return false;
        
        String displayName = item.getItemMeta().getDisplayName();
        return displayName != null && displayName.contains("Đũa Phép");
    }
}
