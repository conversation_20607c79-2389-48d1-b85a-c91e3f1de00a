package shyrcs.ReactionElemental;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import shyrcs.Utils.HexColor;
import shyrcs.Manager.BuffNotificationManager;
import shyrcs.Manager.MonsterStatusManager;
import shyrcs.Manager.SmartComboDetector;

import java.util.*;
import java.util.UUID;

/**
 * Quản lý toàn bộ hệ thống combo phép thuật
 * 
 * COMBO TYPES:
 * 
 * 1. SEQUENTIAL COMBO (Combo Tuần Tự):
 *    - Cast các phép theo thứ tự cụ thể
 *    - Ví dụ: WaterBolt → FlameLance → WindBlade = "Steam Tornado"
 * 
 * 2. TIMING COMBO (Combo Thời Gian):
 *    - Cast nhiều phép cùng lúc hoặc trong khoảng thời gian ngắn
 *    - Ví dụ: Cast 3 phép khác nhau trong 2 giây = "Elemental Burst"
 * 
 * 3. LOCATION COMBO (Combo Vị Trí):
 *    - Cast phép tại cùng một vị trí để tạo hiệu ứng đặc biệt
 *    - Ví dụ: Cast FlameLance và WaterBolt tại cùng 1 block = "Explosion"
 * 
 * 4. CHAIN COMBO (Combo Chuỗi):
 *    - Combo kích hoạt combo khác
 *    - Ví dụ: Vaporize → Steam Cloud → Lightning Strike
 */
public class ComboManager {
    
    // Lưu trữ lịch sử cast spell của mỗi player
    private static final Map<UUID, List<SpellCast>> playerSpellHistory = new HashMap<>();
    
    // Thời gian tối đa để combo có hiệu lực
    private static final long COMBO_TIMEOUT = 5000; // 5 giây
    private static final int MAX_HISTORY_SIZE = 10; // Lưu tối đa 10 spell gần nhất
    
    /**
     * Class lưu thông tin một lần cast spell
     */
    public static class SpellCast {
        public final String spellName;
        public final String element;
        public final Location location;
        public final long timestamp;
        
        public SpellCast(String spellName, String element, Location location) {
            this.spellName = spellName;
            this.element = element;
            this.location = location.clone();
            this.timestamp = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > COMBO_TIMEOUT;
        }
        
        public boolean isNearLocation(Location other, double maxDistance) {
            return location.distance(other) <= maxDistance;
        }
    }
    
    /**
     * Đăng ký spell cast và kiểm tra combo
     */
    public static void registerSpellCast(Player player, String spellName, Location location) {
        UUID playerId = player.getUniqueId();
        String element = getSpellElement(spellName);
        
        // Khởi tạo history nếu chưa có
        if (!playerSpellHistory.containsKey(playerId)) {
            playerSpellHistory.put(playerId, new ArrayList<>());
        }
        
        List<SpellCast> history = playerSpellHistory.get(playerId);
        
        // Thêm spell cast mới
        SpellCast newCast = new SpellCast(spellName, element, location);
        history.add(newCast);

        // Sử dụng SmartComboDetector để quyết định có nên check advanced combos
        SmartComboDetector.ComboDecision decision = SmartComboDetector.getInstance()
            .analyzeSpellCast(player, spellName, location, false); // false = không hit monster cụ thể

        // Debug combo tracking
        System.out.println("DEBUG - ComboManager: " + player.getName() + " cast " + spellName + " (" + element + ")");
        System.out.println("DEBUG - SmartDetector decision: " + decision.reason);
        System.out.println("DEBUG - Current history: " + history.stream()
            .filter(cast -> !cast.isExpired())
            .map(cast -> cast.spellName)
            .reduce((a, b) -> a + " → " + b)
            .orElse("empty"));

        // Giới hạn kích thước history
        while (history.size() > MAX_HISTORY_SIZE) {
            history.remove(0);
        }

        // Loại bỏ spell cũ đã hết hạn
        history.removeIf(SpellCast::isExpired);

        // Chỉ kiểm tra advanced combos nếu SmartDetector cho phép
        if (!decision.shouldSkipAdvancedCombo()) {
            System.out.println("DEBUG - Checking advanced combos...");
            checkSequentialCombo(player, history);
            checkTimingCombo(player, history);
            checkLocationCombo(player, history, location);
            checkChainCombo(player, history);
        } else {
            System.out.println("DEBUG - Skipping advanced combos: " + decision.reason);
        }
        
        // Cập nhật cho hệ thống phản ứng 2 nguyên tố
        if (history.size() >= 2) {
            SpellCast lastCast = history.get(history.size() - 2);
            if (!lastCast.isExpired()) {
                PyroxHydro.checkAndTriggerCombo(player, spellName, location);
            }
        }
    }
    
    /**
     * SEQUENTIAL COMBO: Kiểm tra combo theo thứ tự
     */
    private static void checkSequentialCombo(Player player, List<SpellCast> history) {
        if (history.size() < 3) return;
        
        // Lấy 3 spell gần nhất
        SpellCast third = history.get(history.size() - 1);
        SpellCast second = history.get(history.size() - 2);
        SpellCast first = history.get(history.size() - 3);
        
        // Kiểm tra thời gian (phải trong vòng 5 giây)
        if (first.isExpired()) return;
        
        // Định nghĩa các combo sequence
        String sequence = first.spellName + "→" + second.spellName + "→" + third.spellName;
        System.out.println("DEBUG - Sequential combo check: " + sequence);

        switch (sequence) {
            case "WaterBolt→FlameLance→WindBlade":
                System.out.println("DEBUG - STEAM TORNADO combo detected!");
                executeSteamTornado(player, third.location);
                break;
            case "FlameLance→WaterBolt→FlameLance":
                System.out.println("DEBUG - VOLCANIC ERUPTION combo detected!");
                executeVolcanicEruption(player, third.location);
                break;
            case "WindBlade→WaterBolt→WindBlade":
                System.out.println("DEBUG - HURRICANE combo detected!");
                executeHurricane(player, third.location);
                break;
            default:
                System.out.println("DEBUG - No sequential combo match for: " + sequence);
                break;
        }
    }
    
    /**
     * TIMING COMBO: Combo dựa trên thời gian
     */
    private static void checkTimingCombo(Player player, List<SpellCast> history) {
        if (history.size() < 3) return;
        
        long now = System.currentTimeMillis();
        
        // Đếm số spell cast trong 2 giây gần nhất
        long rapidCastCount = history.stream()
            .filter(cast -> now - cast.timestamp <= 2000)
            .count();
        
        if (rapidCastCount >= 3) {
            // Kiểm tra có đủ đa dạng nguyên tố không
            Set<String> elements = new HashSet<>();
            history.stream()
                .filter(cast -> now - cast.timestamp <= 2000)
                .forEach(cast -> elements.add(cast.element));
                
            if (elements.size() >= 3) {
                executeElementalBurst(player, history.get(history.size() - 1).location);
            } else if (rapidCastCount >= 4) {
                executeRapidFire(player, history.get(history.size() - 1).location);
            }
        }
    }
    
    /**
     * LOCATION COMBO: Combo dựa trên vị trí
     */
    private static void checkLocationCombo(Player player, List<SpellCast> history, Location currentLocation) {
        if (history.size() < 2) return;
        
        // Tìm spell cast tại cùng vị trí (trong bán kính 2 block)
        List<SpellCast> nearbySpells = new ArrayList<>();
        for (SpellCast cast : history) {
            if (!cast.isExpired() && cast.isNearLocation(currentLocation, 2.0)) {
                nearbySpells.add(cast);
            }
        }
        
        if (nearbySpells.size() >= 3) {
            // Kiểm tra có ít nhất 2 nguyên tố khác nhau
            Set<String> elements = new HashSet<>();
            nearbySpells.forEach(cast -> elements.add(cast.element));
            
            if (elements.size() >= 2) {
                executeElementalOverload(player, currentLocation);
            }
        }
    }
    
    /**
     * CHAIN COMBO: Combo kích hoạt combo khác
     */    private static void checkChainCombo(Player player, List<SpellCast> history) {
        // Logic phức tạp cho chain combo
        // Ví dụ: nếu đã thực hiện 3 combo trong 30 giây → Ultimate combo
        // PlayerData.PlayerMagicData data = PlayerData.getInstance().getPlayerData(player);
        
        // Đếm số combo đã thực hiện gần đây (cần thêm tracking vào PlayerData)
        // if (data.getRecentCombos() >= 3) {
        //     executeUltimateCombo(player, history.get(history.size() - 1).location);
        // }
    }
    
    // ===== COMBO EXECUTION METHODS =====
    
    private static void executeSteamTornado(Player player, Location location) {
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "🌪️ STEAM TORNADO! (Sequential Combo)"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "+ 50 EXP (Advanced Combo)"));
        
        // Add combo notification to action bar
        BuffNotificationManager.getInstance().addComboNotification(player, "Steam Tornado", 50, 150);
        
        // Tạo tornado effect với steam particles
        location.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, location, 100, 3, 10, 3, 0.1);
        location.getWorld().spawnParticle(org.bukkit.Particle.SPLASH, location, 80, 2, 8, 2, 0.2);
        
        // Damage và knockback enemies trong vùng
        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 5, 5, 5)) {
            if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                org.bukkit.entity.LivingEntity target = (org.bukkit.entity.LivingEntity) entity;
                target.damage(15.0, player);
                org.bukkit.util.Vector knockback = target.getLocation().subtract(location).toVector().normalize().multiply(2);
                target.setVelocity(knockback);
                
                // Apply steam elemental status to affected monsters
                MonsterStatusManager.getInstance().applyElementalStatus(target, "Steam", 100, player);
            }
        }
          // Sound effect
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 0.8f);
    }
    
    private static void executeVolcanicEruption(Player player, Location location) {
        player.sendMessage(HexColor.toLegacy("#ff4500", "🌋 VOLCANIC ERUPTION! (Pattern Combo)"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "+ 75 EXP (Master Combo)"));
        
        // Add combo notification to action bar with enhanced fire damage
        BuffNotificationManager.getInstance().addComboNotification(player, "Volcanic Eruption", 75, 200);
        BuffNotificationManager.getInstance().addDamageBoost(player, "Pyro", 75, 200);
        
        // Tạo eruption effect với lava particles và knockback
        for (int i = 0; i < 30; i++) {
            final int wave = i;
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    // Tạo explosion rings
                    double radius = wave * 0.5;
                    for (double angle = 0; angle < 360; angle += 15) {
                        double radians = Math.toRadians(angle);
                        double x = radius * Math.cos(radians);
                        double z = radius * Math.sin(radians);
                        
                        Location explosionLoc = location.clone().add(x, 0, z);
                        explosionLoc.getWorld().spawnParticle(org.bukkit.Particle.LAVA, explosionLoc, 10, 0.5, 0.5, 0.5, 0);
                        explosionLoc.getWorld().spawnParticle(org.bukkit.Particle.FLAME, explosionLoc, 15, 0.8, 0.8, 0.8, 0.1);
                        
                        // Damage trong ring
                        for (org.bukkit.entity.Entity entity : explosionLoc.getWorld().getNearbyEntities(explosionLoc, 2, 2, 2)) {
                            if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                                ((org.bukkit.entity.LivingEntity) entity).damage(8.0, player);
                                entity.setFireTicks(100); // Burn for 5 seconds
                                
                                // Apply burning elemental status to affected monsters
                                MonsterStatusManager.getInstance().applyElementalStatus((org.bukkit.entity.LivingEntity) entity, "Burning", 120, player);
                            }
                        }
                    }
                }
            }.runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("MagicCore"), wave * 3L);
        }
          // Sound effects
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 2.0f, 0.8f);
        location.getWorld().playSound(location, org.bukkit.Sound.BLOCK_FIRE_AMBIENT, 1.5f, 0.5f);
    }
    
    private static void executeHurricane(Player player, Location location) {
        player.sendMessage(HexColor.toLegacy("#7dd3c0", "💨 HURRICANE! (Wind Pattern)"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "+ 60 EXP (Element Mastery)"));
        
        // Add combo notification to action bar with wind boost
        BuffNotificationManager.getInstance().addComboNotification(player, "Hurricane", 60, 180);
        BuffNotificationManager.getInstance().addDamageBoost(player, "Anemo", 60, 180);
        
        // Tạo hurricane effect với wind particles và pull effect
        for (int i = 0; i < 60; i++) {
            final int duration = i;
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    // Tạo spiral wind effect
                    for (int layer = 0; layer < 3; layer++) {
                        double radius = 4.0 - (layer * 1.0);
                        double height = layer * 2.0;
                        
                        for (double angle = 0; angle < 360; angle += 20) {
                            double spiralAngle = angle + (duration * 15) + (layer * 120);
                            double radians = Math.toRadians(spiralAngle);
                            double x = radius * Math.cos(radians);
                            double z = radius * Math.sin(radians);
                            
                            Location windLoc = location.clone().add(x, height, z);
                            windLoc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, windLoc, 5, 0.2, 0.2, 0.2, 0.05);
                            windLoc.getWorld().spawnParticle(org.bukkit.Particle.SWEEP_ATTACK, windLoc, 3, 0.1, 0.1, 0.1, 0);
                        }
                    }
                    
                    // Pull entities vào center
                    for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 8, 6, 8)) {
                        if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                            org.bukkit.util.Vector pullForce = location.subtract(entity.getLocation()).toVector().normalize().multiply(0.3);
                            pullForce.setY(0.1); // Slight upward force
                            entity.setVelocity(entity.getVelocity().add(pullForce));
                            
                            if (entity.getLocation().distance(location) < 3) {
                                ((org.bukkit.entity.LivingEntity) entity).damage(4.0, player);
                                
                                // Apply wind elemental status to affected monsters
                                MonsterStatusManager.getInstance().applyElementalStatus((org.bukkit.entity.LivingEntity) entity, "Hurricane", 100, player);
                            }
                        }
                    }
                }
            }.runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("MagicCore"), duration * 2L);
        }
          // Sound effects
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_ENDER_DRAGON_FLAP, 1.5f, 1.5f);
        location.getWorld().playSound(location, org.bukkit.Sound.BLOCK_WOOL_BREAK, 2.0f, 0.1f);
    }
    
    private static void executeElementalBurst(Player player, Location location) {
        player.sendMessage(HexColor.toLegacy("#ff6b9d", "✨ ELEMENTAL BURST! (Timing Combo)"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "+ 100 EXP (Perfect Timing)"));
        
        // Add combo notification to action bar with elemental mastery
        BuffNotificationManager.getInstance().addComboNotification(player, "Elemental Burst", 100, 250);
        BuffNotificationManager.getInstance().addElementalStatus(player, "Elemental Mastery", 200);
        
        // Tạo massive burst với multiple elements
        for (int wave = 0; wave < 5; wave++) {
            final int currentWave = wave;
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    double radius = 3.0 + (currentWave * 2.0);
                    
                    // Tạo concentric circles với different elements
                    for (double angle = 0; angle < 360; angle += 10) {
                        double radians = Math.toRadians(angle);
                        double x = radius * Math.cos(radians);
                        double z = radius * Math.sin(radians);
                        
                        Location burstLoc = location.clone().add(x, 1, z);
                        
                        // Randomize element effects
                        switch (currentWave % 3) {
                            case 0: // Fire
                                burstLoc.getWorld().spawnParticle(org.bukkit.Particle.FLAME, burstLoc, 8, 0.5, 0.5, 0.5, 0.1);
                                break;
                            case 1: // Water
                                burstLoc.getWorld().spawnParticle(org.bukkit.Particle.SPLASH, burstLoc, 8, 0.5, 0.5, 0.5, 0.1);
                                break;
                            case 2: // Wind
                                burstLoc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, burstLoc, 8, 0.5, 0.5, 0.5, 0.1);
                                break;
                        }
                    }
                    
                    // Damage enemies trong wave
                    for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, radius + 1, 4, radius + 1)) {
                        if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                            double distance = entity.getLocation().distance(location);
                            if (Math.abs(distance - radius) < 2.0) {
                                ((org.bukkit.entity.LivingEntity) entity).damage(12.0, player);
                                
                                // Random elemental effect and status
                                String[] elements = {"Pyro", "Hydro", "Anemo"};
                                String elementType = elements[currentWave % 3];
                                MonsterStatusManager.getInstance().applyElementalStatus((org.bukkit.entity.LivingEntity) entity, elementType, 80, player);
                                
                                switch (currentWave % 3) {
                                    case 0: entity.setFireTicks(60); break;
                                    case 1: ((org.bukkit.entity.LivingEntity) entity).addPotionEffect(
                                        new org.bukkit.potion.PotionEffect(org.bukkit.potion.PotionEffectType.SLOWNESS, 60, 1)); break;
                                    case 2: entity.setVelocity(entity.getVelocity().add(new org.bukkit.util.Vector(0, 0.5, 0))); break;
                                }
                            }
                        }
                    }
                }
            }.runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("MagicCore"), currentWave * 10L);
        }
          // Sound effects
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 1.8f, 1.2f);
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 2.0f);
    }
    
    private static void executeRapidFire(Player player, Location location) {
        player.sendMessage(HexColor.toLegacy("#ffeb3b", "⚡ RAPID FIRE! (Speed Combo)"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "+ 40 EXP (Speed Bonus)"));
        
        // Add combo notification to action bar with speed boost
        BuffNotificationManager.getInstance().addComboNotification(player, "Rapid Fire", 40, 120);
        BuffNotificationManager.getInstance().addElementalStatus(player, "Attack Speed", 120);
        
        // Tạo rapid projectile barrage
        org.bukkit.util.Vector direction = player.getLocation().getDirection();
        
        for (int i = 0; i < 15; i++) {
            final int shot = i;
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    // Tạo slight spread cho projectiles
                    org.bukkit.util.Vector spread = direction.clone();
                    spread.add(new org.bukkit.util.Vector(
                        (Math.random() - 0.5) * 0.3,
                        (Math.random() - 0.5) * 0.2,
                        (Math.random() - 0.5) * 0.3
                    ));
                    spread.normalize().multiply(2.0);
                    
                    // Spawn projectile trail
                    Location startLoc = player.getEyeLocation().add(direction.clone().multiply(1.5));
                    for (int step = 0; step < 20; step++) {
                        final int currentStep = step;
                        new org.bukkit.scheduler.BukkitRunnable() {
                            @Override
                            public void run() {
                                Location projectileLoc = startLoc.clone().add(spread.clone().multiply(currentStep * 0.5));
                                
                                // Particle trail
                                projectileLoc.getWorld().spawnParticle(org.bukkit.Particle.CRIT, projectileLoc, 3, 0.1, 0.1, 0.1, 0);
                                projectileLoc.getWorld().spawnParticle(org.bukkit.Particle.ENCHANTED_HIT, projectileLoc, 2, 0.1, 0.1, 0.1, 0);
                                
                                // Check for hits
                                for (org.bukkit.entity.Entity entity : projectileLoc.getWorld().getNearbyEntities(projectileLoc, 1, 1, 1)) {
                                    if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                                        ((org.bukkit.entity.LivingEntity) entity).damage(3.0, player);
                                        projectileLoc.getWorld().spawnParticle(org.bukkit.Particle.CRIT, entity.getLocation(), 8, 0.5, 0.5, 0.5, 0);
                                        
                                        // Apply rapid fire elemental status to hit monsters
                                        MonsterStatusManager.getInstance().applyElementalStatus((org.bukkit.entity.LivingEntity) entity, "Rapid Fire", 60, player);
                                        
                                        this.cancel();
                                        return;
                                    }
                                }
                            }
                        }.runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("MagicCore"), currentStep * 1L);
                    }
                }
            }.runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("MagicCore"), shot * 3L);
        }
        
        // Sound effects
        for (int i = 0; i < 15; i++) {
            final int sound = i;
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_ARROW_SHOOT, 0.5f, 1.5f + (sound * 0.1f));
                }            }.runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("MagicCore"), sound * 3L);
        }
    }
    
    private static void executeElementalOverload(Player player, Location location) {
        player.sendMessage(HexColor.toLegacy("#9c27b0", "💥 ELEMENTAL OVERLOAD! (Location Combo)"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "+ 80 EXP (Spatial Mastery)"));
        
        // Add combo notification to action bar with elemental overload
        BuffNotificationManager.getInstance().addComboNotification(player, "Elemental Overload", 80, 300);
        BuffNotificationManager.getInstance().addElementalStatus(player, "Overload", 250);
        
        // Tạo massive elemental explosion tại vị trí
        for (int phase = 0; phase < 3; phase++) {
            final int currentPhase = phase;
            new org.bukkit.scheduler.BukkitRunnable() {
                @Override
                public void run() {
                    // Phase 1: Implosion (pull in)
                    if (currentPhase == 0) {
                        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 8, 8, 8)) {
                            if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                                org.bukkit.util.Vector pullForce = location.subtract(entity.getLocation()).toVector().normalize().multiply(0.8);
                                entity.setVelocity(pullForce);
                            }
                        }
                        location.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, location, 100, 4, 4, 4, 2);
                        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 2.0f, 0.5f);
                    }
                    // Phase 2: Charging
                    else if (currentPhase == 1) {
                        for (int i = 0; i < 20; i++) {
                            double angle = Math.random() * 360;
                            double radius = 2 + Math.random() * 3;
                            double x = radius * Math.cos(Math.toRadians(angle));
                            double z = radius * Math.sin(Math.toRadians(angle));
                            
                            Location chargeLoc = location.clone().add(x, Math.random() * 3, z);
                            chargeLoc.getWorld().spawnParticle(org.bukkit.Particle.HAPPY_VILLAGER, chargeLoc, 5, 0.1, 0.1, 0.1, 0.1);
                            chargeLoc.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, chargeLoc, 3, 0.1, 0.1, 0.1, 0);
                        }
                        location.getWorld().playSound(location, org.bukkit.Sound.BLOCK_BEACON_POWER_SELECT, 1.5f, 2.0f);
                    }
                    // Phase 3: Explosion
                    else {
                        // Massive explosion with mixed elements
                        location.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, location, 5, 1, 1, 1, 0);
                        location.getWorld().spawnParticle(org.bukkit.Particle.FLAME, location, 80, 5, 3, 5, 0.3);
                        location.getWorld().spawnParticle(org.bukkit.Particle.SPLASH, location, 60, 4, 2, 4, 0.5);
                        location.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, location, 40, 3, 2, 3, 0.2);
                        location.getWorld().spawnParticle(org.bukkit.Particle.HAPPY_VILLAGER, location, 100, 6, 4, 6, 0.8);
                        
                        // Massive damage trong radius
                        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 10, 10, 10)) {
                            if (entity instanceof org.bukkit.entity.LivingEntity && entity != player) {
                                double distance = entity.getLocation().distance(location);
                                double damage = 25.0 * (1.0 - (distance / 10.0)); // Damage giảm theo khoảng cách
                                ((org.bukkit.entity.LivingEntity) entity).damage(Math.max(damage, 5.0), player);
                                
                                // Apply overload elemental status to affected monsters
                                MonsterStatusManager.getInstance().applyElementalStatus((org.bukkit.entity.LivingEntity) entity, "Overload", 150, player);
                                
                                // Knockback
                                org.bukkit.util.Vector knockback = entity.getLocation().subtract(location).toVector();
                                knockback.normalize().multiply(3.0).setY(1.0);
                                entity.setVelocity(knockback);
                                
                                // Random elemental effects
                                int effect = (int) (Math.random() * 3);
                                switch (effect) {
                                    case 0: entity.setFireTicks(100); break;
                                    case 1: 
                                        ((org.bukkit.entity.LivingEntity) entity).addPotionEffect(
                                            new org.bukkit.potion.PotionEffect(org.bukkit.potion.PotionEffectType.SLOWNESS, 100, 2)); 
                                        break;
                                    case 2: 
                                        ((org.bukkit.entity.LivingEntity) entity).addPotionEffect(
                                            new org.bukkit.potion.PotionEffect(org.bukkit.potion.PotionEffectType.POISON, 80, 1)); 
                                        break;
                                }
                            }
                        }
                        
                        // Sound effects
                        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 3.0f, 0.8f);
                        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 2.0f, 1.2f);
                    }
                }
            }.runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("MagicCore"), currentPhase * 30L);
        }
    }
    
    /**
     * Lấy nguyên tố của spell
     */
    private static String getSpellElement(String spellName) {
        if (spellName == null) return "None";
        
        switch (spellName) {
            case "FlameLance": return "Pyro";
            case "WaterBolt": return "Hydro";
            case "WindBlade": return "Anemo";
            default: return "None";
        }
    }
    
    /**
     * Clear history của player khi logout
     */
    public static void clearPlayerHistory(UUID playerId) {
        playerSpellHistory.remove(playerId);
    }
    
    /**
     * Debug method để xem history
     */
    public static void showPlayerHistory(Player player) {
        List<SpellCast> history = playerSpellHistory.get(player.getUniqueId());
        if (history == null || history.isEmpty()) {
            player.sendMessage("No spell history found.");
            return;
        }
        
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== SPELL HISTORY ==="));
        for (int i = history.size() - 1; i >= 0; i--) {
            SpellCast cast = history.get(i);
            long ago = (System.currentTimeMillis() - cast.timestamp) / 1000;
            player.sendMessage(String.format("%d. %s (%s) - %ds ago", 
                history.size() - i, cast.spellName, cast.element, ago));
        }
    }
}
