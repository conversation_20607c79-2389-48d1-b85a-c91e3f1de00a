package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.Manager.ActionBarManager;
import shyrcs.Utils.HexColor;

public class ActionBarCommand implements CommandExecutor {
    
    private final ActionBarManager actionBarManager;
    
    public ActionBarCommand() {
        this.actionBarManager = ActionBarManager.getInstance();
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(HexColor.toLegacy("#ff6b6b", "Chỉ người chơi mới có thể sử dụng lệnh này!"));
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            // Toggle action bar
            if (actionBarManager.isActive(player)) {
                actionBarManager.stopActionBar(player);
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "✗ Đã tắt Action Bar"));
            } else {
                actionBarManager.startActionBar(player);
                player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã bật Action Bar"));
            }
            return true;
        }
        
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "on":
            case "enable":
                actionBarManager.startActionBar(player);
                player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã bật Action Bar"));
                break;
                
            case "off":
            case "disable":
                actionBarManager.stopActionBar(player);
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "✗ Đã tắt Action Bar"));
                break;
                
            case "reload":
            case "refresh":
                if (actionBarManager.isActive(player)) {
                    actionBarManager.updateImmediately(player);
                    player.sendMessage(HexColor.toLegacy("#4fc3f7", "🔄 Đã làm mới Action Bar"));
                } else {
                    player.sendMessage(HexColor.toLegacy("#ffeb3b", "⚠ Action Bar chưa được bật!"));
                }
                break;
                
            case "status":
                if (actionBarManager.isActive(player)) {
                    player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Action Bar đang hoạt động"));
                } else {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "✗ Action Bar đang tắt"));
                }
                break;
                
            default:
                player.sendMessage(HexColor.toLegacy("#ffeb3b", "Sử dụng:"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/actionbar - Toggle on/off"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/actionbar on/off - Bật/tắt"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/actionbar reload - Làm mới"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/actionbar status - Kiểm tra trạng thái"));
                break;
        }
        
        return true;
    }
}
