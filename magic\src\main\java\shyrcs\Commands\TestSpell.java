package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.Player.PlayerData;
import shyrcs.Utils.HexColor;

public class TestSpell implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            // Hiển thị thông tin hiện tại
            PlayerData playerData = new PlayerData();
            PlayerData.PlayerMagicData data = playerData.getPlayerData(player);
            
            player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== SPELL DEBUG INFO ==="));
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Selected Skill: '" + data.getSelectedSkill() + "'"));
            player.sendMessage(HexColor.toLegacy("#4fc3f7", "Learned Spells: " + data.getLearnedSpells()));
            player.sendMessage(HexColor.toLegacy("#4caf50", "Mana: " + (int)data.getMana() + "/" + (int)data.getMaxMana()));
            
            // Test chant cho spell hiện tại
            String selectedSpell = data.getSelectedSkill();
            if (selectedSpell != null && !selectedSpell.isEmpty()) {
                String chant = getSpellChant(selectedSpell);
                player.sendMessage(HexColor.toLegacy("#ff9800", "Chant for '" + selectedSpell + "': '" + chant + "'"));
            }
            
            return true;
        }
        
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "set":
                if (args.length < 2) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Usage: /testspell set <spellname>"));
                    return true;
                }
                
                String spellName = args[1];
                PlayerData playerData = new PlayerData();
                PlayerData.PlayerMagicData data = playerData.getPlayerData(player);
                
                data.setSelectedSkill(spellName);
                player.sendMessage(HexColor.toLegacy("#4caf50", "Set selected spell to: " + spellName));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "Chant: " + getSpellChant(spellName)));
                break;
                
            case "chant":
                if (args.length < 2) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Usage: /testspell chant <spellname>"));
                    return true;
                }
                
                String spell = args[1];
                String chant = getSpellChant(spell);
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "Chant for '" + spell + "': '" + chant + "'"));
                break;
                
            default:
                player.sendMessage(HexColor.toLegacy("#ffeb3b", "Usage:"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/testspell - Show current info"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/testspell set <spell> - Set selected spell"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/testspell chant <spell> - Test chant"));
                break;
        }
        
        return true;
    }
    
    private String getSpellChant(String spellName) {
        switch (spellName) {
            case "FlameLance":
                return "flahmeh lahnkeh";
            case "WaterBolt":
                return "wahtehr bohlt";
            case "WindBlade":
                return "wihnd blahdeh";
            default:
                return "???";
        }
    }
}
