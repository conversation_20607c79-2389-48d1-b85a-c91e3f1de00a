trả lời bằng tiếng việt

tạo dự án ma thuật cực phức tạp

có 10 nguyên tố, hiện chỉ cần viết một nguên tố là Pyro và các nguyên tố kia vẫn thêm vô nhưng sẽ chưa làm gì được 

Màu chính của Pyro: #ec4343 

ProfilePlayer.java
🔧 **Cấu trúc GUI:**

* **Kích thước:** 6 hàng × 9 ô (tổng 54 slot)
* **Chức năng từng slot:**

| Vị trí | Nội dung                       | Gợi ý item                                                    |
| ------ | ------------------------------ | ------------------------------------------------------------- |
| 4      | Tên người chơi + cấp độ        | `BOOK` hoặc `PLAYER_HEAD`                                     |
| 10     | Thu<PERSON><PERSON> t<PERSON> (HP, Mana, Stamina) | `TOTEM_OF_UNDYING`                                            |
| 12     | Buffs / Debuffs hiện tại       | `POTION`                                                      |
| 14     | Danh sách phép đã học          | `BLAZE_POWDER` hoặc `ENCHANTED_BOOK` *(click vào mở GUI mới)* |
| 16     | Kỹ năng chiến đấu / đặc biệt   | `IRON_SWORD` hoặc `FIREWORK_STAR`                             |
| 20     | Kháng nguyên tố                | `SHIELD`                                                      |
| 22     | Thống kê tổng quát             | `MAP`                                                         |
| 24     | Danh hiệu / danh vọng          | `NAME_TAG`                                                    |
| 31     | Placeholder (block trang trí)  | Tùy chọn                                                      |
| 38     | Slot Rune 1                    | `LAPIS_LAZULI`                                                |
| 39     | Slot Rune 2                    | `GLOWSTONE_DUST`                                              |
| 41     | Slot Rune 3                    | `REDSTONE`                                                    |
| 42     | Slot Rune 4                    | `QUARTZ`                                                      |
| 49     | Nút Thoát GUI                  | `BARRIER`                                                     |

---

✨ **Yêu cầu bổ sung:**

* **Text:** Dùng màu sắc và định dạng `&l`, `&6`, `&b`, `&5` để tạo cảm giác huyền bí cho tên và lore item.
* **Lore:** Mỗi item nên có mô tả chi tiết bằng lore (thông tin phụ).
* **Tương tác:** Khi người chơi nhấn vào slot 14 (phép đã học), mở ra GUI mới chứa danh sách phép chi tiết.
* **Action bar:** Hiển thị liên tục thông tin gồm:

  ```
  HP | Mana | Tên phép đang áp dụng cho gậy
  ```


Spellbook.java:
GUI Phép Đã Học (Spellbook Menu)
Đề xuất bố cục (36 slot → 4 hàng):
Mỗi ô là 1 phép đã học (icon = ENCHANTED_BOOK hoặc custom icon theo hệ)

tên Gui:
<Tên Hệ(Dùng HexColor)> | <Cấp Độ(Dùng HexColor Tương Đương Với Cấp Độ Đó)>

Hiện thông tin phép bằng lore
Display Name: "<>" (theo tên màu của nguyên tố đó)
Lore:
- "#e6f2f0Mô tả: #f0edcc<>"
- "#e6f2f0Mana: #3766f7<>"
- "#e6f2f0Hồi chiêu: #c9abed<>"
- "#e6f2f0Velkath Rune: #972c46<>"
- "#e6f2f0Niệm Chú: #bdeeee<>"

slot 27 và 35 là nút chuyển trang
từ slot 28 -> 35 sẽ là chuyển sang gui (Dùng để lọc phép theo cấp độ (nhấn vào để mở danh sách spell tương ứng)

| Slot | Cấp bậc               | Biểu tượng gợi ý             | Màu sắc        | Lore                                 |
| ---- | --------------------- | ---------------------------- | -------------- | ------------------------------------ |
| 28   | 🟢 **Novice**         | `WOODEN_SWORD`               |  (lục nhạt) | "Phép nhập môn"                      |
| 29   | 🔵 **Apprentice**     | `BOOK`                       | (xanh biển) | "Đang học nghề"                      |
| 30   | 🟡 **Adept**          | `IRON_SWORD`                 |  (vàng)     | "Đã thuần thục phép cơ bản"          |
| 31   | 🔮 **Expert**         | `BLAZE_ROD`                  | (tím)       | "Hiểu sâu ma thuật"                  |
| 32   | 🔥 **Master**         | `NETHERITE_SWORD`            | (đỏ nhạt)  | "Pháp sư tinh thông"                 |
| 33   | 🌟 **Legendary**      | `NETHER_STAR`                | (vàng đậm)  | "Hiếm có, truyền thuyết"             |
| 34   | 🐉 **Mythic**         | `DRAGON_EGG` / `DRAGON_HEAD` | (hồng tím) | "Bậc huyền thoại, vượt qua thực tại" |
| 35   | 🔐 **Khoá / chưa mở** | `BARRIER`                    |              | "Chưa đạt yêu cầu để mở cấp này"     |

SelectSkill.java
Chọn skill cho đũa phép (qua gui Spellbook.java)

WoodWand.java
đơn giản chỉ là vật phẩm đũa phép 

khi chuột phải sẽ mở gui Spellbook.java

Lore

- ""
- "Select Skill: <>"
- ""
- "Signature: <>"

GiveWand.java
magic givewand <player>=<Signature> <tên wand>

Signature có tên player thì tức là chỉ có player đó sài được

FlameLance.java
Loại: Tấn công tầm xa
Miêu tả: Người chơi triệu hồi một ngọn thương lửa bay thẳng về phía trước, gây sát thương nổ nhỏ khi va chạm và thiêu đốt mục tiêu.

Hiệu ứng:

Gọi ra một viên đạn lửa bay nhanh (ví dụ: Fireball không phá block).

Khi chạm vào kẻ địch hoặc block, phát nổ nhẹ (radius 2).

Gây sát thương 4–6 HP và đốt cháy mục tiêu trong 5 giây.

Particle: flame, lava drip, hiệu ứng cháy xé gió.

Âm thanh: entity.blaze.shoot, entity.generic.explode.

Cooldown: 5 giây
Mana cost: 20

Tên phép:     Flame Lance
Runes:   ᚠᛚᚨᛗᛖ ᛚᚨᚾᚲᛖ
Niệm Chú:  flahmeh lahnkeh

khi thi chiển skill này:
cần chat "flahmeh lahnkeh" (check skill đang chọn, nếu không phải skill đang chọn thì chat sẽ không thi triển)

skill thi triển sẽ theo tâm của nhân vật 

HexColor.java
hỗ trợ hex color

ý tưởng cho một file quản lý và nhận biết toàn bộ file trong folder Magic bởi vì code sẽ rất nhiều và khó quản lý nên tôi cần gì đó tự động nhất biết file ma thuật (cho biết file đó hệ nào và phẩm chất nào) src/main/java/shyrcs/Magic/<Hệ>/<Phẩm Chất>/<File Skill>.java

