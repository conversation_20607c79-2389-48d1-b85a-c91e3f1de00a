package shyrcs.Player;

import org.bukkit.entity.Player;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.List;
import java.util.ArrayList;

public class PlayerData {

    private static PlayerData instance;
    private Map<UUID, PlayerMagicData> playerDataMap = new HashMap<>();

    private PlayerData() {
        // Private constructor for singleton
    }

    public static PlayerData getInstance() {
        if (instance == null) {
            instance = new PlayerData();
        }
        return instance;
    }

    public PlayerMagicData getPlayerData(Player player) {
        PlayerMagicData data = playerDataMap.computeIfAbsent(player.getUniqueId(),
            k -> new PlayerMagicData(player.getName()));

        System.out.println("DEBUG - PlayerData.getPlayerData for " + player.getName() +
                          ", selectedSkill: '" + data.getSelectedSkill() + "'");
        return data;
    }

    /**
     * <PERSON><PERSON><PERSON> t<PERSON>t cả dữ liệu ng<PERSON><PERSON>i chơi
     */
    public void saveAllPlayerData() {
        // TODO: Implement save to file/database
        System.out.println("Saving " + playerDataMap.size() + " player data entries...");
    }
    
    public static class PlayerMagicData {
        private String playerName;
        private int level = 1;
        private int experience = 0;
        private double mana = 100.0;
        private double maxMana = 100.0;
        private String selectedElement = "Pyro"; // Mặc định Pyro
        private String selectedSkill = "";
        private List<String> learnedSpells = new ArrayList<>();
        private Map<String, Long> skillCooldowns = new HashMap<>();
        private Map<String, Integer> elementalResistances = new HashMap<>();
        private List<String> titles = new ArrayList<>();
        private int monstersKilled = 0;
        private int spellsUsed = 0;
        private long playTime = 0;
        
        public PlayerMagicData(String playerName) {
            this.playerName = playerName;
            // Thêm phép cơ bản để test
            learnedSpells.add("FlameLance");
            learnedSpells.add("WaterBolt");
            learnedSpells.add("WindBlade");
            // Khởi tạo kháng nguyên tố
            initializeResistances();
        }
        
        private void initializeResistances() {
            elementalResistances.put("Fire", 0);
            elementalResistances.put("Water", 0);
            elementalResistances.put("Air", 0);
            elementalResistances.put("Earth", 0);
            elementalResistances.put("Lightning", 0);
            elementalResistances.put("Nature", 0);
            elementalResistances.put("Ice", 0);
            elementalResistances.put("Light", 0);
            elementalResistances.put("Dark", 0);
        }
        
        // Getters và Setters
        public String getPlayerName() { return playerName; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
        public int getExperience() { return experience; }
        public void setExperience(int experience) { this.experience = experience; }
        public double getMana() { return mana; }
        public void setMana(double mana) { this.mana = Math.min(mana, maxMana); }
        public double getMaxMana() { return maxMana; }
        public void setMaxMana(double maxMana) { this.maxMana = maxMana; }
        public String getSelectedElement() { return selectedElement; }
        public void setSelectedElement(String selectedElement) { this.selectedElement = selectedElement; }
        public String getSelectedSkill() {
            System.out.println("DEBUG - getSelectedSkill() returning: '" + selectedSkill + "'");
            return selectedSkill;
        }
        public void setSelectedSkill(String selectedSkill) {
            System.out.println("DEBUG - setSelectedSkill() called with: '" + selectedSkill + "'");
            System.out.println("DEBUG - Previous value was: '" + this.selectedSkill + "'");
            this.selectedSkill = selectedSkill;
            System.out.println("DEBUG - New value set to: '" + this.selectedSkill + "'");
        }
        public List<String> getLearnedSpells() { return learnedSpells; }
        public Map<String, Long> getSkillCooldowns() { return skillCooldowns; }
        public Map<String, Integer> getElementalResistances() { return elementalResistances; }
        public List<String> getTitles() { return titles; }
        public int getMonstersKilled() { return monstersKilled; }
        public void setMonstersKilled(int monstersKilled) { this.monstersKilled = monstersKilled; }
        public int getSpellsUsed() { return spellsUsed; }
        public void setSpellsUsed(int spellsUsed) { this.spellsUsed = spellsUsed; }
        public long getPlayTime() { return playTime; }
        public void setPlayTime(long playTime) { this.playTime = playTime; }
        
        public boolean isOnCooldown(String skill) {
            Long cooldownEnd = skillCooldowns.get(skill);
            if (cooldownEnd == null) return false;
            return System.currentTimeMillis() < cooldownEnd;
        }
        
        public void setCooldown(String skill, long cooldownTime) {
            skillCooldowns.put(skill, System.currentTimeMillis() + cooldownTime);
        }
        
        public long getRemainingCooldown(String skill) {
            Long cooldownEnd = skillCooldowns.get(skill);
            if (cooldownEnd == null) return 0;
            return Math.max(0, cooldownEnd - System.currentTimeMillis());
        }
    }
}
