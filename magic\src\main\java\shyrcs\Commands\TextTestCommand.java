package shyrcs.Commands;

import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import shyrcs.Utils.ItemBuilder;
import shyrcs.Utils.MessageUtil;

public class TextTestCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length < 1) {
            player.sendMessage(MessageUtil.hex("#ffeb3b", "Usage:"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/texttest basic - Test basic colors"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/texttest shadow - Test shadow effects"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/texttest item - Test item with various effects"));
            return true;
        }
        
        String testType = args[0].toLowerCase();
        
        switch (testType) {
            case "basic":
                testBasicColors(player);
                break;
                
            case "shadow":
                testShadowEffects(player);
                break;
                
            case "item":
                testItemEffects(player);
                break;
                
            default:
                player.sendMessage(MessageUtil.hex("#ff6b6b", "Unknown test type: " + testType));
                break;
        }
        
        return true;
    }
    
    private void testBasicColors(Player player) {
        player.sendMessage(MessageUtil.hex("#ffeb3b", "=== BASIC COLOR TESTS ==="));
        
        // Test basic colors
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><red>Red text</red>"));
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><blue>Blue text</blue>"));
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><green>Green text</green>"));
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><yellow>Yellow text</yellow>"));
        
        // Test hex colors
        player.sendMessage(MessageUtil.hex("#FF5555", "Hex Red"));
        player.sendMessage(MessageUtil.hex("#5555FF", "Hex Blue"));
        player.sendMessage(MessageUtil.hex("#55FF55", "Hex Green"));
        
        // Test verbose color syntax
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><color:#FF5555>Verbose Hex Red</color>"));
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><color:yellow>Verbose Yellow</color>"));
    }
    
    private void testShadowEffects(Player player) {
        player.sendMessage(MessageUtil.hex("#ffeb3b", "=== SHADOW EFFECT TESTS ==="));
        
        // Test shadow with named colors
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><shadow:black><yellow>Yellow with black shadow</yellow></shadow>"));
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><shadow:red><white>White with red shadow</white></shadow>"));
        
        // Test shadow with hex colors
        player.sendMessage(MessageUtil.shadow("#000000", "#FFFFFF", "White with black shadow (method)"));
        player.sendMessage(MessageUtil.shadow("#FF0000", "#FFFF00", "Yellow with red shadow (method)"));
        
        // Test shadow with alpha
        player.sendMessage(MessageUtil.shadowAlpha("#000000", 0.8f, "#FFFFFF", "White with strong black shadow"));
        player.sendMessage(MessageUtil.shadowAlpha("#0000FF", 0.3f, "#FFFFFF", "White with light blue shadow"));
        
        // Test disable shadow
        player.sendMessage(MessageUtil.getComponentParsed("<!italic><!shadow><red>Red with no shadow</red>"));
    }
    
    private void testItemEffects(Player player) {
        player.sendMessage(MessageUtil.hex("#ffeb3b", "=== ITEM EFFECT TESTS ==="));
        
        // Test item 1: Basic colors
        ItemStack item1 = new ItemBuilder(Material.DIAMOND_SWORD)
            .setDisplayName("<!italic><color:#FF5555>Crimson Blade</color>")
            .setLore(
                "",
                "<!italic><color:#AAAAAA>A legendary sword forged in fire</color>",
                "<!italic><color:#55FF55>+ 10 Attack Damage</color>",
                "<!italic><color:#5555FF>+ 5 Magic Power</color>"
            )
            .build();
        player.getInventory().addItem(item1);
        
        // Test item 2: Shadow effects
        ItemStack item2 = new ItemBuilder(Material.GOLDEN_SWORD)
            .setDisplayName("<!italic><shadow:#000000><color:#FFD700>Golden Excalibur</color></shadow>")
            .setLore(
                "",
                "<!italic><shadow:#333333><color:#FFFFFF>A blade of pure light</color></shadow>",
                "<!italic><color:#FFAA00>⚔ Legendary Weapon</color>",
                "<!italic><color:#FF5555>❤ +20 Health</color>"
            )
            .build();
        player.getInventory().addItem(item2);
        
        // Test item 3: Gradient and complex effects
        ItemStack item3 = new ItemBuilder(Material.NETHERITE_SWORD)
            .setDisplayName("<!italic><gradient:#FF0000:#0000FF>Void Reaper</gradient>")
            .setLore(
                "",
                "<!italic><gradient:#800080:#FF00FF>Forged in the depths of the void</gradient>",
                "<!italic><shadow:#000000:0.5><color:#FFFFFF>✦ Mythic Rarity</color></shadow>",
                "<!italic><color:#FF5555>💀 Instant Kill Chance</color>",
                "<!italic><color:#5555FF>🌀 Void Magic</color>"
            )
            .build();
        player.getInventory().addItem(item3);
        
        player.sendMessage(MessageUtil.hex("#4caf50", "✓ Added 3 test items to your inventory!"));
    }
}
