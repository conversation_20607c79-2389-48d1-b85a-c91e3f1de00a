package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.Manager.SmartComboDetector;
import shyrcs.Utils.HexColor;

public class SmartComboTest implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            showHelp(player);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "demo1":
                demoSimpleReaction(player);
                break;
            case "demo2":
                demoAdvancedCombo(player);
                break;
            case "demo3":
                demoHybridMode(player);
                break;
            case "reset":
                SmartComboDetector.getInstance().resetPlayerContext(player.getUniqueId());
                player.sendMessage(HexColor.toLegacy("#4caf50", "✅ Reset combo context!"));
                break;
            default:
                showHelp(player);
                break;
        }
        
        return true;
    }
    
    private void showHelp(Player player) {
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== SMART COMBO TEST ==="));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/smartcombo demo1 - Demo Simple Elemental Reaction"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/smartcombo demo2 - Demo Advanced Combo"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/smartcombo demo3 - Demo Hybrid Mode"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/smartcombo reset - Reset combo context"));
    }
    
    /**
     * Demo 1: Simple Elemental Reaction
     * Fast casting on monster → Simple reaction only
     */
    private void demoSimpleReaction(Player player) {
        player.sendMessage(HexColor.toLegacy("#ffeb3b", "🧪 DEMO 1: Simple Elemental Reaction"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "Scenario: Fast casting on monster"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "1. Summon zombie"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "2. Cast: wahtehr bohlt (hit zombie)"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "3. Cast: flahmeh lahnkeh (hit zombie) - trong 1.5s"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "Expected: STEAM CLOUD reaction only, no advanced combo"));
        
        // Simulate the scenario
        SmartComboDetector detector = SmartComboDetector.getInstance();
        
        // Reset context
        detector.resetPlayerContext(player.getUniqueId());
        
        // Simulate WaterBolt hit monster
        SmartComboDetector.ComboDecision decision1 = detector.analyzeSpellCast(player, "WaterBolt", player.getLocation(), true);
        player.sendMessage(HexColor.toLegacy("#87ceeb", "WaterBolt: " + decision1.reason));
        
        // Wait a bit then simulate FlameLance hit monster
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
            () -> {
                SmartComboDetector.ComboDecision decision2 = detector.analyzeSpellCast(player, "FlameLance", player.getLocation(), true);
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "FlameLance: " + decision2.reason));
                player.sendMessage(HexColor.toLegacy("#4caf50", "✅ Simple reaction: " + decision2.allowSimpleReaction));
                player.sendMessage(HexColor.toLegacy("#ff9800", "❌ Advanced combo: " + decision2.allowAdvancedCombo));
            }, 20L // 1 second
        );
    }
    
    /**
     * Demo 2: Advanced Combo
     * Sequential casting without hitting monsters → Advanced combo only
     */
    private void demoAdvancedCombo(Player player) {
        player.sendMessage(HexColor.toLegacy("#ffeb3b", "🧪 DEMO 2: Advanced Combo"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "Scenario: Sequential casting pattern"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "1. Cast: wahtehr bohlt (no monster hit)"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "2. Cast: flahmeh lahnkeh (no monster hit) - after 2s"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "3. Cast: wihnd blahdeh (no monster hit) - after 2s"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "Expected: STEAM TORNADO advanced combo only"));
        
        // Simulate the scenario
        SmartComboDetector detector = SmartComboDetector.getInstance();
        
        // Reset context
        detector.resetPlayerContext(player.getUniqueId());
        
        // Simulate WaterBolt (no monster hit)
        SmartComboDetector.ComboDecision decision1 = detector.analyzeSpellCast(player, "WaterBolt", player.getLocation(), false);
        player.sendMessage(HexColor.toLegacy("#87ceeb", "WaterBolt: " + decision1.reason));
        
        // Wait then simulate FlameLance (no monster hit)
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
            () -> {
                SmartComboDetector.ComboDecision decision2 = detector.analyzeSpellCast(player, "FlameLance", player.getLocation(), false);
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "FlameLance: " + decision2.reason));
                
                // Wait then simulate WindBlade (no monster hit)
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
                    () -> {
                        SmartComboDetector.ComboDecision decision3 = detector.analyzeSpellCast(player, "WindBlade", player.getLocation(), false);
                        player.sendMessage(HexColor.toLegacy("#4caf50", "WindBlade: " + decision3.reason));
                        player.sendMessage(HexColor.toLegacy("#ff9800", "❌ Simple reaction: " + decision3.allowSimpleReaction));
                        player.sendMessage(HexColor.toLegacy("#4caf50", "✅ Advanced combo: " + decision3.allowAdvancedCombo));
                    }, 40L // 2 seconds
                );
            }, 40L // 2 seconds
        );
    }
    
    /**
     * Demo 3: Hybrid Mode
     * Fast casting that hits monster but also forms pattern → Both allowed
     */
    private void demoHybridMode(Player player) {
        player.sendMessage(HexColor.toLegacy("#ffeb3b", "🧪 DEMO 3: Hybrid Mode"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "Scenario: Fast casting with monster hit + pattern"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "1. Cast: wahtehr bohlt (hit monster)"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "2. Cast: flahmeh lahnkeh (hit monster) - within 1s"));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "3. Cast: wihnd blahdeh (hit monster) - within 1s"));
        player.sendMessage(HexColor.toLegacy("#4caf50", "Expected: Both simple reactions AND advanced combo"));
        
        // Simulate the scenario
        SmartComboDetector detector = SmartComboDetector.getInstance();
        
        // Reset context
        detector.resetPlayerContext(player.getUniqueId());
        
        // Simulate rapid casting with monster hits
        SmartComboDetector.ComboDecision decision1 = detector.analyzeSpellCast(player, "WaterBolt", player.getLocation(), true);
        player.sendMessage(HexColor.toLegacy("#87ceeb", "WaterBolt: " + decision1.reason));
        
        // Very fast follow-up
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
            () -> {
                SmartComboDetector.ComboDecision decision2 = detector.analyzeSpellCast(player, "FlameLance", player.getLocation(), true);
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "FlameLance: " + decision2.reason));
                
                // Very fast follow-up
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
                    () -> {
                        SmartComboDetector.ComboDecision decision3 = detector.analyzeSpellCast(player, "WindBlade", player.getLocation(), true);
                        player.sendMessage(HexColor.toLegacy("#4caf50", "WindBlade: " + decision3.reason));
                        player.sendMessage(HexColor.toLegacy("#4caf50", "✅ Simple reaction: " + decision3.allowSimpleReaction));
                        player.sendMessage(HexColor.toLegacy("#4caf50", "✅ Advanced combo: " + decision3.allowAdvancedCombo));
                        player.sendMessage(HexColor.toLegacy("#ff6b9d", "🎉 HYBRID MODE: Both systems active!"));
                    }, 10L // 0.5 seconds
                );
            }, 10L // 0.5 seconds
        );
    }
}
