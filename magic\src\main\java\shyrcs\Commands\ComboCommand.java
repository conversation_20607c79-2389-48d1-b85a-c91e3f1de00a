package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.ReactionElemental.ComboManager;
import shyrcs.Utils.HexColor;

public class ComboCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            showComboHelp(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "history":
                ComboManager.showPlayerHistory(player);
                break;
                
            case "test":
                if (args.length < 2) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Usage: /combo test <spellname>"));
                    return true;
                }
                testCombo(player, args[1]);
                break;
                
            case "clear":
                ComboManager.clearPlayerHistory(player.getUniqueId());
                player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã xóa lịch sử combo"));
                break;
                
            case "guide":
                showComboGuide(player);
                break;
                
            default:
                showComboHelp(player);
                break;
        }
        
        return true;
    }
    
    private void showComboHelp(Player player) {
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== COMBO SYSTEM ==="));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/combo history - Xem lịch sử phép thuật"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/combo test <spell> - Test combo với spell"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/combo clear - Xóa lịch sử combo"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "/combo guide - Hướng dẫn combo"));
    }
    
    private void testCombo(Player player, String spellName) {
        // Giả lập cast spell để test combo
        ComboManager.registerSpellCast(player, spellName, player.getLocation());
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "🧪 Test cast: " + spellName));
    }
    
    private void showComboGuide(Player player) {
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== COMBO GUIDE ==="));
        player.sendMessage("");
        
        player.sendMessage(HexColor.toLegacy("#ff6b6b", "🔥 ELEMENTAL REACTIONS:"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• Pyro → Hydro = Vaporize (+50% Pyro damage)"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• Hydro → Pyro = Steam Cloud (blindness area)"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• Pyro + Hydro (same time) = Boiling Water (DoT area)"));
        player.sendMessage("");
        
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "🌪️ SEQUENTIAL COMBOS:"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• WaterBolt → FlameLance → WindBlade = Steam Tornado"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• FlameLance → WaterBolt → FlameLance = Volcanic Eruption"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• WindBlade → WaterBolt → WindBlade = Hurricane"));
        player.sendMessage("");
        
        player.sendMessage(HexColor.toLegacy("#ffeb3b", "⚡ TIMING COMBOS:"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• 3+ spells in 2 seconds = Rapid Fire"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• 3+ different elements in 2s = Elemental Burst"));
        player.sendMessage("");
        
        player.sendMessage(HexColor.toLegacy("#9c27b0", "📍 LOCATION COMBOS:"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• 3+ spells at same location = Elemental Overload"));
        player.sendMessage("");
        
        player.sendMessage(HexColor.toLegacy("#4caf50", "💡 Tips:"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• Combo window: 5 seconds"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• Use /combo history to track your casts"));
        player.sendMessage(HexColor.toLegacy("#ffffff", "• Practice with /combo test <spell>"));
    }
}
