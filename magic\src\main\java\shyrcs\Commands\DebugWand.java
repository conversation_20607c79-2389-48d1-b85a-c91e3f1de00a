package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import shyrcs.Utils.HexColor;

@SuppressWarnings("deprecation")
public class DebugWand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || item.getItemMeta() == null) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn cần cầm một item trong tay!"));
            return true;
        }
        
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== DEBUG WAND INFO ==="));

        // Display name debug
        if (item.getItemMeta().displayName() != null) {
            net.kyori.adventure.text.Component displayNameComponent = item.getItemMeta().displayName();
            String plainDisplayName = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(displayNameComponent);
            String legacyDisplayName = net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer.legacySection().serialize(displayNameComponent);

            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Display Name (Plain): " + plainDisplayName));
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Display Name (Legacy): " + legacyDisplayName));
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Display Name (Rendered):"));
            player.sendMessage(displayNameComponent);
        } else {
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Display Name: " + item.getItemMeta().getDisplayName()));
        }

        player.sendMessage(HexColor.toLegacy("#4fc3f7", "Material: " + item.getType()));
        
        // Check both legacy lore and component lore
        if (item.getItemMeta().lore() != null) {
            player.sendMessage(HexColor.toLegacy("#4caf50", "Component Lore:"));
            java.util.List<net.kyori.adventure.text.Component> lore = item.getItemMeta().lore();
            for (int i = 0; i < lore.size(); i++) {
                net.kyori.adventure.text.Component lineComponent = lore.get(i);
                String plainText = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(lineComponent);
                String legacyText = net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer.legacySection().serialize(lineComponent);

                player.sendMessage(HexColor.toLegacy("#ffffff", "  " + i + " Plain: '" + plainText + "'"));
                player.sendMessage(HexColor.toLegacy("#90ee90", "     Legacy: '" + legacyText + "'"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "     Rendered:"));
                player.sendMessage(lineComponent); // Hiển thị component thực tế
            }
        } else if (item.getItemMeta().getLore() != null) {
            player.sendMessage(HexColor.toLegacy("#4caf50", "Legacy Lore:"));
            for (int i = 0; i < item.getItemMeta().getLore().size(); i++) {
                String lore = item.getItemMeta().getLore().get(i);
                String cleanLore = lore.replaceAll("§[0-9a-fk-or]", "");
                player.sendMessage(HexColor.toLegacy("#ffffff", "  " + i + ": '" + lore + "'"));
                player.sendMessage(HexColor.toLegacy("#90ee90", "     Clean: '" + cleanLore + "'"));
            }
        } else {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không có lore"));
        }
        
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "==================="));
        
        return true;
    }
}
