package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import shyrcs.Utils.HexColor;

public class DebugWand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || item.getItemMeta() == null) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Bạn cần cầm một item trong tay!"));
            return true;
        }
        
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== DEBUG WAND INFO ==="));
        player.sendMessage(HexColor.toLegacy("#ffeb3b", "Display Name: " + item.getItemMeta().getDisplayName()));
        player.sendMessage(HexColor.toLegacy("#4fc3f7", "Material: " + item.getType()));
        
        if (item.getItemMeta().getLore() != null) {
            player.sendMessage(HexColor.toLegacy("#4caf50", "Lore:"));
            for (int i = 0; i < item.getItemMeta().getLore().size(); i++) {
                String lore = item.getItemMeta().getLore().get(i);
                String cleanLore = lore.replaceAll("§[0-9a-fk-or]", "");
                player.sendMessage(HexColor.toLegacy("#ffffff", "  " + i + ": '" + lore + "'"));
                player.sendMessage(HexColor.toLegacy("#90ee90", "     Clean: '" + cleanLore + "'"));
            }
        } else {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không có lore"));
        }
        
        player.sendMessage(HexColor.toLegacy("#4ecdc4", "==================="));
        
        return true;
    }
}
