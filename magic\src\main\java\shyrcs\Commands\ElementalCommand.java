package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import shyrcs.Manager.MonsterStatusManager;
import shyrcs.Utils.HexColor;

public class ElementalCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length < 1) {
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Usage:"));
            player.sendMessage(HexColor.toLegacy("#4fc3f7", "/elemental apply <element> [duration] - Áp dụng element cho mob gần nhất"));
            player.sendMessage(HexColor.toLegacy("#4fc3f7", "/elemental clear - Xóa tất cả status"));
            player.sendMessage(HexColor.toLegacy("#4fc3f7", "/elemental test - Test VAPORIZE"));
            return true;
        }
        
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "apply":
                if (args.length < 2) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Usage: /elemental apply <element> [duration]"));
                    return true;
                }
                
                String element = args[1];
                int duration = args.length > 2 ? Integer.parseInt(args[2]) : 10;
                
                // Tìm mob gần nhất
                LivingEntity nearestMob = findNearestMob(player);
                if (nearestMob == null) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không tìm thấy mob nào gần đây!"));
                    return true;
                }
                
                // Áp dụng status
                MonsterStatusManager.getInstance().applyElementalStatus(nearestMob, element, duration, player);
                player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã áp dụng " + element + " cho " + nearestMob.getType() + " trong " + duration + "s"));
                break;
                
            case "clear":
                // Xóa tất cả status
                LivingEntity mob = findNearestMob(player);
                if (mob != null) {
                    MonsterStatusManager.getInstance().removeElementalStatus(mob);
                    player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã xóa status của " + mob.getType()));
                } else {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Không tìm thấy mob nào!"));
                }
                break;
                
            case "test":
                // Test VAPORIZE
                LivingEntity testMob = findNearestMob(player);
                if (testMob == null) {
                    player.sendMessage(HexColor.toLegacy("#ff6b6b", "Cần có mob để test!"));
                    return true;
                }
                
                player.sendMessage(HexColor.toLegacy("#ffeb3b", "Testing VAPORIZE reaction..."));
                
                // Áp dụng Pyro trước
                MonsterStatusManager.getInstance().applyElementalStatus(testMob, "Pyro", 15, player);
                player.sendMessage(HexColor.toLegacy("#ff6b6b", "1. Áp dụng Pyro status"));
                
                // Delay rồi áp dụng Hydro
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 
                    () -> {
                        MonsterStatusManager.getInstance().applyElementalStatus(testMob, "Hydro", 15, player);
                        player.sendMessage(HexColor.toLegacy("#4fc3f7", "2. Áp dụng Hydro status → VAPORIZE!"));
                    }, 
                    40L // 2 giây
                );
                break;
                
            default:
                player.sendMessage(HexColor.toLegacy("#ffeb3b", "Commands:"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/elemental apply <element> [duration]"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/elemental clear"));
                player.sendMessage(HexColor.toLegacy("#4fc3f7", "/elemental test"));
                break;
        }
        
        return true;
    }
    
    private LivingEntity findNearestMob(Player player) {
        LivingEntity nearest = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (Entity entity : player.getNearbyEntities(10, 10, 10)) {
            if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                double distance = player.getLocation().distance(entity.getLocation());
                if (distance < nearestDistance) {
                    nearest = (LivingEntity) entity;
                    nearestDistance = distance;
                }
            }
        }
        
        return nearest;
    }
}
