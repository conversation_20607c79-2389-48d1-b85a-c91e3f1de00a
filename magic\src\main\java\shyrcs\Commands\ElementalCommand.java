package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import shyrcs.Manager.MonsterStatusManager;
import shyrcs.Utils.MessageUtil;

public class ElementalCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Chỉ người chơi mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length < 1) {
            player.sendMessage(MessageUtil.hex("#ffeb3b", "Usage:"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental apply <element> [duration] - Áp dụng element cho mob gần nhất"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental clear - Xóa tất cả status"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental test - Test VAPORIZE"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental steam - Test STEAM CLOUD"));
            player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental damage - Test real-time HP update"));
            return true;
        }
        
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "apply":
                if (args.length < 2) {
                    player.sendMessage(MessageUtil.hex("#ff6b6b", "Usage: /elemental apply <element> [duration]"));
                    return true;
                }

                String element = args[1];
                int duration = args.length > 2 ? Integer.parseInt(args[2]) : 10;

                // Tìm mob gần nhất
                LivingEntity nearestMob = findNearestMob(player);
                if (nearestMob == null) {
                    player.sendMessage(MessageUtil.hex("#ff6b6b", "Không tìm thấy mob nào gần đây!"));
                    return true;
                }

                // Áp dụng status
                MonsterStatusManager.getInstance().applyElementalStatus(nearestMob, element, duration, player);
                player.sendMessage(MessageUtil.hex("#4caf50", "✓ Đã áp dụng " + element + " cho " + nearestMob.getType() + " trong " + duration + "s"));
                break;
                
            case "clear":
                // Xóa tất cả status
                LivingEntity mob = findNearestMob(player);
                if (mob != null) {
                    MonsterStatusManager.getInstance().removeElementalStatus(mob);
                    player.sendMessage(MessageUtil.hex("#4caf50", "✓ Đã xóa status của " + mob.getType()));
                } else {
                    player.sendMessage(MessageUtil.hex("#ff6b6b", "Không tìm thấy mob nào!"));
                }
                break;

            case "test":
                // Test VAPORIZE
                LivingEntity testMob = findNearestMob(player);
                if (testMob == null) {
                    player.sendMessage(MessageUtil.hex("#ff6b6b", "Cần có mob để test!"));
                    return true;
                }

                player.sendMessage(MessageUtil.hex("#ffeb3b", "Testing VAPORIZE reaction..."));

                // Áp dụng Pyro trước
                MonsterStatusManager.getInstance().applyElementalStatus(testMob, "Pyro", 15, player);
                player.sendMessage(MessageUtil.hex("#ff6b6b", "1. Áp dụng Pyro status"));

                // Delay rồi áp dụng Hydro
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
                    () -> {
                        MonsterStatusManager.getInstance().applyElementalStatus(testMob, "Hydro", 15, player);
                        player.sendMessage(MessageUtil.hex("#4fc3f7", "2. Áp dụng Hydro status → VAPORIZE!"));
                    },
                    40L // 2 giây
                );
                break;

            case "steam":
                // Test STEAM CLOUD
                LivingEntity steamMob = findNearestMob(player);
                if (steamMob == null) {
                    player.sendMessage(MessageUtil.hex("#ff6b6b", "Cần có mob để test!"));
                    return true;
                }

                player.sendMessage(MessageUtil.hex("#ffeb3b", "Testing STEAM CLOUD reaction..."));

                // Áp dụng Hydro trước
                MonsterStatusManager.getInstance().applyElementalStatus(steamMob, "Hydro", 15, player);
                player.sendMessage(MessageUtil.hex("#4fc3f7", "1. Áp dụng Hydro status"));

                // Delay rồi áp dụng Pyro
                org.bukkit.Bukkit.getScheduler().runTaskLater(
                    org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
                    () -> {
                        MonsterStatusManager.getInstance().applyElementalStatus(steamMob, "Pyro", 15, player);
                        player.sendMessage(MessageUtil.hex("#ff6b6b", "2. Áp dụng Pyro status → STEAM CLOUD!"));
                    },
                    40L // 2 giây
                );
                break;

            case "damage":
                // Test real-time HP update
                LivingEntity damageMob = findNearestMob(player);
                if (damageMob == null) {
                    player.sendMessage(MessageUtil.hex("#ff6b6b", "Cần có mob để test!"));
                    return true;
                }

                player.sendMessage(MessageUtil.hex("#ffeb3b", "Testing real-time HP update..."));

                // Áp dụng status trước
                MonsterStatusManager.getInstance().applyElementalStatus(damageMob, "Pyro", 30, player);

                // Gây damage liên tục để test real-time update
                new org.bukkit.scheduler.BukkitRunnable() {
                    int count = 0;

                    @Override
                    public void run() {
                        if (count >= 10 || damageMob.isDead()) {
                            this.cancel();
                            player.sendMessage(MessageUtil.hex("#4caf50", "✓ Test hoàn thành!"));
                            return;
                        }

                        // Gây 2 damage mỗi 0.5 giây
                        damageMob.damage(2.0);
                        player.sendMessage(MessageUtil.hex("#ff6b6b", "Damage " + (count + 1) + "/10 - HP should update instantly!"));
                        count++;
                    }
                }.runTaskTimer(org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"), 0L, 10L);
                break;

            default:
                player.sendMessage(MessageUtil.hex("#ffeb3b", "Commands:"));
                player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental apply <element> [duration]"));
                player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental clear"));
                player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental test"));
                player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental steam"));
                player.sendMessage(MessageUtil.hex("#4fc3f7", "/elemental damage"));
                break;
        }
        
        return true;
    }
    
    private LivingEntity findNearestMob(Player player) {
        LivingEntity nearest = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (Entity entity : player.getNearbyEntities(10, 10, 10)) {
            if (entity instanceof LivingEntity && !(entity instanceof Player)) {
                double distance = player.getLocation().distance(entity.getLocation());
                if (distance < nearestDistance) {
                    nearest = (LivingEntity) entity;
                    nearestDistance = distance;
                }
            }
        }
        
        return nearest;
    }
}
