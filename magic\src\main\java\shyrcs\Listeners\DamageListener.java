package shyrcs.Listeners;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityRegainHealthEvent;
import shyrcs.Manager.MonsterStatusManager;

/**
 * Listener để cập nhật HP real-time cho Monster Status
 */
public class DamageListener implements Listener {
    
    /**
     * Cập nhật HP khi monster bị damage
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof LivingEntity)) return;
        
        LivingEntity entity = (LivingEntity) event.getEntity();
        
        // Chỉ cập nhật cho monsters (không phải players)
        if (entity instanceof Player) return;
        
        // Delay 1 tick để HP được cập nhật sau khi damage được apply
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
            () -> {
                if (entity.isValid() && !entity.isDead()) {
                    // Force update HP ngay lập tức
                    MonsterStatusManager.getInstance().forceUpdateHP(entity);
                }
            },
            1L // 1 tick delay
        );
    }
    
    /**
     * Cập nhật HP khi monster hồi máu
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityRegainHealth(EntityRegainHealthEvent event) {
        if (!(event.getEntity() instanceof LivingEntity)) return;
        
        LivingEntity entity = (LivingEntity) event.getEntity();
        
        // Chỉ cập nhật cho monsters (không phải players)
        if (entity instanceof Player) return;
        
        // Delay 1 tick để HP được cập nhật sau khi heal được apply
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            org.bukkit.Bukkit.getPluginManager().getPlugin("Magic"),
            () -> {
                if (entity.isValid() && !entity.isDead()) {
                    // Force update HP ngay lập tức
                    MonsterStatusManager.getInstance().forceUpdateHP(entity);
                }
            },
            1L // 1 tick delay
        );
    }
    
    /**
     * Xử lý khi monster chết
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDeath(org.bukkit.event.entity.EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        
        // Chỉ xử lý monsters (không phải players)
        if (entity instanceof Player) return;
        
        // Xóa status khi monster chết
        MonsterStatusManager.getInstance().removeElementalStatus(entity);
        
        System.out.println("DEBUG - " + entity.getType() + " died, removed elemental status");
    }
}
