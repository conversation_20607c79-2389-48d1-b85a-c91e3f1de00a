package shyrcs.Manager;

import org.bukkit.entity.Player;
import org.bukkit.Location;
import java.util.*;

/**
 * Smart Combo Detector - Phân biệt giữa Simple Elemental Reactions và Advanced Combos
 * Dựa trên context và timing để quyết định loại combo nào được kích ho<PERSON>
 */
public class SmartComboDetector {
    
    private static SmartComboDetector instance;
    
    // Track player intentions
    private static final Map<UUID, ComboContext> playerContexts = new HashMap<>();
    
    // Timing thresholds
    private static final long SIMPLE_REACTION_WINDOW = 1500; // 1.5 giây cho simple reactions
    private static final long ADVANCED_COMBO_WINDOW = 4000;  // 4 giây cho advanced combos
    private static final long RAPID_CAST_THRESHOLD = 800;    // 0.8 giây = rapid casting
    
    public static SmartComboDetector getInstance() {
        if (instance == null) {
            instance = new SmartComboDetector();
        }
        return instance;
    }
    
    /**
     * Context tracking cho mỗi player
     */
    public static class ComboContext {
        public String lastSpell;
        public String lastElement;
        public long lastCastTime;
        public Location lastLocation;
        public boolean isRapidCasting;
        public int consecutiveCasts;
        public boolean hasTargetedMonster;
        
        public ComboContext() {
            reset();
        }
        
        public void reset() {
            lastSpell = null;
            lastElement = null;
            lastCastTime = 0;
            lastLocation = null;
            isRapidCasting = false;
            consecutiveCasts = 0;
            hasTargetedMonster = false;
        }
        
        public long getTimeSinceLastCast() {
            if (lastCastTime == 0) {
                return Long.MAX_VALUE; // First cast = infinite time
            }
            return System.currentTimeMillis() - lastCastTime;
        }
    }
    
    /**
     * Phân tích spell cast và quyết định loại combo
     */
    public ComboDecision analyzeSpellCast(Player player, String spellName, Location location, boolean hitMonster) {
        UUID playerId = player.getUniqueId();
        ComboContext context = playerContexts.computeIfAbsent(playerId, k -> new ComboContext());
        
        long currentTime = System.currentTimeMillis();
        long timeSinceLastCast = context.getTimeSinceLastCast();
        
        // Update context
        String element = getSpellElement(spellName);

        // Debug context state BEFORE analysis
        System.out.println("DEBUG - SmartDetector BEFORE: Player=" + player.getName() + 
                         ", Spell=" + spellName + "(" + element + ")" +
                         ", LastSpell=" + context.lastSpell + 
                         ", LastElement=" + context.lastElement + 
                         ", TimeSince=" + timeSinceLastCast + "ms" +
                         ", HitMonster=" + hitMonster);

        // Detect rapid casting
        context.isRapidCasting = timeSinceLastCast < RAPID_CAST_THRESHOLD;
        if (context.isRapidCasting) {
            context.consecutiveCasts++;
        } else {
            context.consecutiveCasts = 1;
        }
        
        context.hasTargetedMonster = hitMonster;
        
        ComboDecision decision = new ComboDecision();
        
        // CASE 0: First cast or too much time passed
        if (context.lastSpell == null || timeSinceLastCast >= ADVANCED_COMBO_WINDOW) {
            // First cast or reset - allow simple reactions if conditions met
            if (hitMonster && context.lastSpell != null && !element.equals(context.lastElement)) {
                decision.allowSimpleReaction = true;
                decision.allowAdvancedCombo = false;
                decision.reason = "Simple reaction: Element change after timeout";
                System.out.println("DEBUG - SmartDetector: SIMPLE REACTION (after timeout) - " + context.lastElement + " + " + element);
            } else if (hitMonster) {
                // Special case: Allow simple reaction even on first cast if hitting monster
                // This covers monster elemental reactions regardless of player casting history
                decision.allowSimpleReaction = true;
                decision.allowAdvancedCombo = false;
                decision.reason = context.lastSpell == null ? "Simple reaction: First cast on monster" : "Simple reaction: Monster hit after timeout";
                System.out.println("DEBUG - SmartDetector: " + decision.reason);
            } else {
                decision.allowSimpleReaction = false;
                decision.allowAdvancedCombo = false;
                decision.reason = context.lastSpell == null ? "First cast (no target)" : "Too much time passed (no target)";
                System.out.println("DEBUG - SmartDetector: " + decision.reason);
            }
        }
        // Analyze combo type based on context (within combo window)
        else {
            
            // CASE 1: Simple Elemental Reaction (prioritized)
            if (shouldTriggerSimpleReaction(context, element, timeSinceLastCast, hitMonster)) {
                decision.allowSimpleReaction = true;
                decision.allowAdvancedCombo = false;
                decision.reason = "Simple reaction: Fast cast on monster with different element";
                
                System.out.println("DEBUG - SmartDetector: SIMPLE REACTION - " + context.lastElement + " + " + element);
            }
            
            // CASE 2: Advanced Combo Intent
            else if (shouldTriggerAdvancedCombo(context, element, timeSinceLastCast)) {
                decision.allowSimpleReaction = false;
                decision.allowAdvancedCombo = true;
                decision.reason = "Advanced combo: Sequential casting pattern detected";
                
                System.out.println("DEBUG - SmartDetector: ADVANCED COMBO - Pattern detected");
            }
            
            // CASE 3: Both allowed (hybrid scenario)
            else if (shouldAllowBoth(context, element, timeSinceLastCast, hitMonster)) {
                decision.allowSimpleReaction = true;
                decision.allowAdvancedCombo = true;
                decision.reason = "Hybrid: Both reactions can occur";
                
                System.out.println("DEBUG - SmartDetector: HYBRID - Both allowed");
            }
            
            // CASE 4: Neither (too slow or same element)
            else {
                decision.allowSimpleReaction = false;
                decision.allowAdvancedCombo = false;
                decision.reason = "No combo: Timing or element mismatch";
                System.out.println("DEBUG - SmartDetector: NO COMBO - " + decision.reason + " (time: " + timeSinceLastCast + "ms, element: " + context.lastElement + " -> " + element + ", hit: " + hitMonster + ", rapid: " + context.isRapidCasting + ")");
            }
        }
        
        // Update context for next cast
        context.lastSpell = spellName;
        context.lastElement = element;
        context.lastCastTime = currentTime;
        context.lastLocation = location.clone();
        
        return decision;
    }
    
    /**
     * Kiểm tra có nên trigger simple elemental reaction
     */
    private boolean shouldTriggerSimpleReaction(ComboContext context, String newElement, long timeSinceLastCast, boolean hitMonster) {
        return hitMonster && // Phải hit monster
               timeSinceLastCast <= SIMPLE_REACTION_WINDOW && // Trong thời gian ngắn
               !newElement.equals(context.lastElement) && // Element khác nhau
               !context.isRapidCasting; // Không phải rapid casting
    }
    
    /**
     * Kiểm tra có nên trigger advanced combo
     */
    private boolean shouldTriggerAdvancedCombo(ComboContext context, String newElement, long timeSinceLastCast) {
        return timeSinceLastCast <= ADVANCED_COMBO_WINDOW && // Trong thời gian dài hơn
               (context.consecutiveCasts >= 2 || // Đã cast nhiều spell
                context.isRapidCasting || // Đang rapid casting
                timeSinceLastCast > SIMPLE_REACTION_WINDOW); // Quá thời gian simple reaction
    }
    
    /**
     * Kiểm tra có nên cho phép cả 2
     */
    private boolean shouldAllowBoth(ComboContext context, String newElement, long timeSinceLastCast, boolean hitMonster) {
        return hitMonster && // Hit monster cho simple reaction
               timeSinceLastCast <= SIMPLE_REACTION_WINDOW && // Trong thời gian simple
               context.consecutiveCasts >= 2 && // Nhưng cũng có pattern cho advanced
               !newElement.equals(context.lastElement); // Element khác nhau
    }
    
    /**
     * Lấy element của spell
     */
    private String getSpellElement(String spellName) {
        switch (spellName) {
            case "FlameLance": return "Pyro";
            case "WaterBolt": return "Hydro";
            case "WindBlade": return "Anemo";
            default: return "None";
        }
    }
    
    /**
     * Reset context khi player logout hoặc idle quá lâu
     */
    public void resetPlayerContext(UUID playerId) {
        playerContexts.remove(playerId);
    }
    
    /**
     * Clean up expired contexts
     */
    public void cleanupExpiredContexts() {
        long now = System.currentTimeMillis();
        playerContexts.entrySet().removeIf(entry -> 
            now - entry.getValue().lastCastTime > ADVANCED_COMBO_WINDOW * 2);
    }
    
    /**
     * Kết quả phân tích combo
     */
    public static class ComboDecision {
        public boolean allowSimpleReaction = false;
        public boolean allowAdvancedCombo = false;
        public String reason = "";
        
        public boolean shouldSkipSimpleReaction() {
            return !allowSimpleReaction;
        }
        
        public boolean shouldSkipAdvancedCombo() {
            return !allowAdvancedCombo;
        }
        
        public boolean allowsBoth() {
            return allowSimpleReaction && allowAdvancedCombo;
        }
    }
}
