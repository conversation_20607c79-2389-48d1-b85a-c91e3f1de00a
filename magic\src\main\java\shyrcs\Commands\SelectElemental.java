package shyrcs.Commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.Player.PlayerData;
import shyrcs.Utils.HexColor;
import shyrcs.Manager.SpellRegistry;

public class SelectElemental implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(HexColor.toLegacy("#ff6b6b", "Chỉ người chơi mới có thể sử dụng lệnh này!"));
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Hiển thị danh sách nguyên tố
            player.sendMessage(HexColor.toLegacy("#4ecdc4", "=== DANH SÁCH NGUYÊN TỐ ==="));
            for (String element : SpellRegistry.ELEMENTS) {
                String color = HexColor.getElementColor(element);
                player.sendMessage(HexColor.toLegacy(color, "• " + element));
            }
            player.sendMessage(HexColor.toLegacy("#ffeb3b", "Sử dụng: /elemental <tên nguyên tố>"));
            return true;
        }

        String element = args[0];

        // Kiểm tra nguyên tố có hợp lệ không
        boolean validElement = false;
        for (String validEl : SpellRegistry.ELEMENTS) {
            if (validEl.equalsIgnoreCase(element)) {
                element = validEl; // Chuẩn hóa tên
                validElement = true;
                break;
            }
        }

        if (!validElement) {
            player.sendMessage(HexColor.toLegacy("#ff6b6b", "Nguyên tố không hợp lệ: " + element));
            return true;
        }

        // Cập nhật nguyên tố cho người chơi
        PlayerData playerData = PlayerData.getInstance();
        PlayerData.PlayerMagicData data = playerData.getPlayerData(player);
        data.setSelectedElement(element);

        String color = HexColor.getElementColor(element);
        player.sendMessage(HexColor.toLegacy("#4caf50", "✓ Đã chọn nguyên tố: ") +
                          HexColor.toLegacy(color, element));

        return true;
    }
}
