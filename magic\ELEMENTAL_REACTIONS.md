# Smart Elemental Reactions System

## Overview
MagicCore có hệ thống phản ứng nguyên tố thông minh với **Smart Combo Detection** tự động phân biệt intent và cho phép cả simple reactions và advanced combos hoạt động song song mà không cần toggle manual.

## 🧠 Smart Detection System
**SmartComboDetector** phân tích context và quyết định loại combo dựa trên:
- **Timing**: Rapid (<0.8s), Fast (<1.5s), Normal (<4s)
- **Target**: Hit monster vs Area casting
- **Pattern**: Sequential vs Random
- **Chain Length**: Consecutive spell count

## 3 Hệ Thống Phản Ứng

## 1. Monster-Level Reactions (MonsterStatusManager)
**Mục đích**: Phản ứng trực tiếp trên monster khi có 2+ elements
**Trigger**: Khi monster đã có 1 element status và bị hit bởi element khác

### VAPORIZE (Pyro existing + Hydro new)
- **Damage**: 8 fixed damage
- **Effect**: Steam particles + sound
- **Message**: "💨 VAPORIZE! Gây 8 sát thương!"

### STEAM CLOUD (Hydro existing + Pyro new)  
- **Effect**: Continuous steam particles for 5 seconds
- **Message**: "☁️ STEAM CLOUD! Tạo đám mây hơi nước che tầm nhìn!"

## 2. Player Combo System (PyroxHydro.java)
**Mục đích**: Buff/effects cho player khi cast spells theo sequence
**Trigger**: Cast 2 spells trong 3 giây window

### VAPORIZE (Pyro → Hydro)
- **Effect**: +50% Pyro damage buff for 10 seconds
- **Visual**: Flame + cloud particles
- **Message**: "🔥 Pyro Boost: +50% sát thương trong 10 giây!"

### STEAM CLOUD (Hydro → Pyro)
- **Effect**: Create area denial with blindness
- **Duration**: 5 seconds
- **Message**: "💨 Steam Cloud: Che tầm nhìn kẻ thù trong 5 giây!"

### BOILING WATER (Simultaneous cast)
- **Effect**: 1❤/second damage in area for 10 seconds
- **Visual**: Lava + flame particles in rotating pattern
- **Message**: "♨️ Boiling Water: 1❤/giây trong 10 giây!"

## 3. Advanced Combo System (ComboManager.java)
**Mục đích**: Complex combos với 3+ spells
**Trigger**: Specific spell sequences

### Sequential Combos
- **Steam Tornado**: WaterBolt→FlameLance→WindBlade
- **Volcanic Eruption**: FlameLance→WaterBolt→FlameLance  
- **Hurricane**: WindBlade→WaterBolt→WindBlade

### Timing Combos
- **Elemental Burst**: 3+ different elements in 2 seconds
- **Rapid Fire**: 4+ spells in 2 seconds

### Location Combos
- **Elemental Overload**: 3+ spells at same location

## Key Differences

| System | Trigger | Target | Effect Type | Complexity |
|--------|---------|--------|-------------|------------|
| **Monster-Level** | Element collision on monster | Individual monster | Direct damage/status | Simple |
| **Player Combo** | Spell sequence by player | Player buff + area | Buff/area denial | Medium |
| **Advanced Combo** | Complex patterns | Area effect | Massive damage/effects | Complex |

## Integration
- **MonsterStatusManager**: Handles individual monster reactions
- **PyroxHydro**: Handles 2-spell player combos  
- **ComboManager**: Handles 3+ spell advanced combos
- All systems can work together for layered effects

## 🎯 Smart Decision Matrix

| Scenario | Timing | Monster Hit | Pattern | Simple Reaction | Advanced Combo |
|----------|--------|-------------|---------|----------------|----------------|
| **Monster Combat** | Fast (<1.5s) | ✅ Yes | Any | ✅ Allowed | ❌ Skipped |
| **Area Clearing** | Normal (>1.5s) | ❌ No | Sequential | ❌ Skipped | ✅ Allowed |
| **Intense Combat** | Fast (<1.5s) | ✅ Yes | Sequential | ✅ Allowed | ✅ Allowed |
| **Slow Casting** | Slow (>4s) | Any | Any | ❌ Skipped | ❌ Skipped |

## 🎮 Usage Examples

### Scenario A: Monster Combat (Simple Only)
```bash
/summon zombie
wahtehr bohlt    # Hit zombie → Hydro status
flahmeh lahnkeh  # Hit zombie quickly → STEAM CLOUD reaction only
# Smart Detection: "Simple reaction: Fast cast on monster with different element"
```

### Scenario B: Area Clearing (Advanced Only)
```bash
wahtehr bohlt    # Cast in area (no monster focus)
# Wait 2 seconds
flahmeh lahnkeh  # Cast in area
# Wait 2 seconds
wihnd blahdeh    # Cast in area → STEAM TORNADO combo only
# Smart Detection: "Advanced combo: Sequential casting pattern detected"
```

### Scenario C: Intense Combat (Hybrid Mode)
```bash
/summon zombie zombie zombie
wahtehr bohlt    # Hit zombie rapidly
flahmeh lahnkeh  # Hit zombie rapidly (0.5s later)
wihnd blahdeh    # Hit zombie rapidly (0.5s later)
# Result: STEAM CLOUD + VAPORIZE + STEAM TORNADO
# Smart Detection: "Hybrid: Both reactions can occur"
```

## 🧪 Testing Commands

### Demo Commands
```bash
/smartcombo demo1  # Demo Simple Reaction Only
/smartcombo demo2  # Demo Advanced Combo Only
/smartcombo demo3  # Demo Hybrid Mode
/smartcombo reset  # Reset combo context
```

### Debug Commands
```bash
/combo history     # View spell casting history
/debugwand         # Analyze wand lore and components
/elemental status  # Check monster elemental status
```
