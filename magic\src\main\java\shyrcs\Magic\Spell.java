package shyrcs.Magic;

import org.bukkit.entity.Player;

public interface Spell {
    
    /**
     * Tên của phép thuật
     */
    String getName();
    
    /**
     * <PERSON><PERSON> tả phép thuật
     */
    String getDescription();
    
    /**
     * Nguyên tố của phép thuật
     */
    String getElement();
    
    /**
     * Cấp độ phép thuật
     */
    String getRank();
    
    /**
     * <PERSON><PERSON> cần thiết
     */
    int getManaCost();
    
    /**
     * Thời gian hồi chiêu (milliseconds)
     */
    long getCooldown();
    
    /**
     * <PERSON><PERSON>
     */
    String getVelkathRune();
    
    /**
     * Niệm chú
     */
    String getChant();
    
    /**
     * Thực thi phép thuật
     */
    boolean cast(Player caster);
    
    /**
     * Kiểm tra điều kiện có thể sử dụng phép
     */
    boolean canCast(Player caster);
}
