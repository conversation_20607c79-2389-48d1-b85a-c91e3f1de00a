package shyrcs.Manager;

import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.Utils.HexColor;

import java.util.*;

/**
 * Quản lý hiển thị buff notifications qua Action Bar
 * Format: HP | Mana | Spell Name | +50% Pyro Dmg
 * Chỉ hiển thị khi có buff, không có buff thì cho ActionBarManager hiển thị bình thường
 */
public class BuffNotificationManager {
    
    private static BuffNotificationManager instance;
    private final Map<UUID, Queue<BuffNotification>> playerNotifications = new HashMap<>();
    private final Map<UUID, BukkitTask> displayTasks = new HashMap<>();
    
    public static BuffNotificationManager getInstance() {
        if (instance == null) {
            instance = new BuffNotificationManager();
        }
        return instance;
    }
    
    /**
     * Enum các loại buff
     */
    public enum BuffType {
        BUFF("✓", "#4caf50"),           // Xanh lá - buff tích cực
        DEBUFF("✗", "#ff6b6b"),         // Đỏ - debuff tiêu cực  
        REACTION("⚡", "#ffeb3b"),       // Vàng - phản ứng nguyên tố
        COMBO("🔥", "#ff9800"),         // Cam - combo spell
        ELEMENTAL("🌟", "#9c27b0"),     // Tím - hiệu ứng nguyên tố
        CUSTOM("ℹ", "#4fc3f7");         // Xanh dương - tùy chỉnh
        
        private final String icon;
        private final String color;
        
        BuffType(String icon, String color) {
            this.icon = icon;
            this.color = color;
        }
        
        public String getIcon() { return icon; }
        public String getColor() { return color; }
    }
    
    /**
     * Class lưu thông tin notification
     */
    public static class BuffNotification {
        public final BuffType type;
        public final String message;
        public final int displayDurationTicks;
        public final long createdTime;
        
        public BuffNotification(BuffType type, String message, int displayDurationTicks) {
            this.type = type;
            this.message = message;
            this.displayDurationTicks = displayDurationTicks;
            this.createdTime = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            long elapsedTime = System.currentTimeMillis() - createdTime;
            return elapsedTime >= (displayDurationTicks * 50L); // 50ms per tick
        }
    }
    
    /**
     * Thêm buff notification cho player
     */
    public void addBuffNotification(Player player, BuffType type, String message, int durationTicks) {
        if (player == null || !player.isOnline()) return;
        
        UUID playerId = player.getUniqueId();
        
        // Khởi tạo queue nếu chưa có
        if (!playerNotifications.containsKey(playerId)) {
            playerNotifications.put(playerId, new LinkedList<>());
        }
        
        Queue<BuffNotification> queue = playerNotifications.get(playerId);
        queue.offer(new BuffNotification(type, message, durationTicks));
        
        // Bắt đầu hiển thị nếu chưa có task
        if (!displayTasks.containsKey(playerId)) {
            startDisplayTask(player);
        }
    }
    
    /**
     * Thêm buff notification với các preset phổ biến
     */
    public void addDamageBoost(Player player, String element, int percentage, int durationTicks) {
        String message = "+" + percentage + "% " + element + " Dmg";
        addBuffNotification(player, BuffType.BUFF, message, durationTicks);
    }
    
    public void addReactionNotification(Player player, String reactionName, int durationTicks) {
        addBuffNotification(player, BuffType.REACTION, reactionName, durationTicks);
    }
    
    public void addComboNotification(Player player, String comboName, int expGain, int durationTicks) {
        String message = comboName + " (+" + expGain + " EXP)";
        addBuffNotification(player, BuffType.COMBO, message, durationTicks);
    }
    
    public void addElementalStatus(Player player, String elementType, int durationTicks) {
        String message = elementType + " Effect";
        addBuffNotification(player, BuffType.ELEMENTAL, message, durationTicks);
    }
    
    /**
     * Kiểm tra player có đang hiển thị buff notification không
     */
    public boolean hasActiveNotifications(Player player) {
        UUID playerId = player.getUniqueId();
        Queue<BuffNotification> queue = playerNotifications.get(playerId);
        return queue != null && !queue.isEmpty();
    }
    
    /**
     * Xóa tất cả notifications của player
     */
    public void clearNotifications(Player player) {
        if (player == null) return;
        
        UUID playerId = player.getUniqueId();
        
        // Xóa queue
        playerNotifications.remove(playerId);
        
        // Dừng task
        BukkitTask task = displayTasks.remove(playerId);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }
    }
    
    /**
     * Bắt đầu task hiển thị notifications
     */
    private void startDisplayTask(Player player) {
        UUID playerId = player.getUniqueId();
        
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (!player.isOnline()) {
                    clearNotifications(player);
                    return;
                }
                
                Queue<BuffNotification> queue = playerNotifications.get(playerId);
                if (queue == null || queue.isEmpty()) {
                    this.cancel();
                    displayTasks.remove(playerId);
                    return;
                }
                
                // Xóa notifications đã hết hạn
                queue.removeIf(BuffNotification::isExpired);
                
                if (queue.isEmpty()) {
                    this.cancel();
                    displayTasks.remove(playerId);
                    playerNotifications.remove(playerId);
                    return;
                }
                
                // Hiển thị notification hiện tại
                BuffNotification current = queue.peek();
                if (current != null) {
                    displayBuffActionBar(player, current);
                }
            }
        }.runTaskTimer(Bukkit.getPluginManager().getPlugin("Magic"), 0L, 10L); // Cập nhật mỗi 0.5 giây
        
        displayTasks.put(playerId, task);
    }
      /**
     * Hiển thị buff action bar với format: HP | Mana | Spell Name | Buff Info
     */
    private void displayBuffActionBar(Player player, BuffNotification notification) {
        try {
            // Lấy PlayerData để có thông tin đầy đủ
            shyrcs.Player.PlayerData playerData = shyrcs.Player.PlayerData.getInstance();
            shyrcs.Player.PlayerData.PlayerMagicData data = playerData.getPlayerData(player);
            
            // Lấy thông tin HP
            int currentHP = (int) player.getHealth();
            int maxHP = 20; // Minecraft default max health
            
            // Lấy thông tin Mana
            int currentMana = (int) data.getMana();
            int maxMana = (int) data.getMaxMana();
            
            // Lấy tên phép đang chọn
            String selectedSpell = data.getSelectedSkill();
            if (selectedSpell == null || selectedSpell.isEmpty()) {
                selectedSpell = "Chưa chọn";
            }
            
            // Tạo action bar text với buff information
            String actionBarText =
                HexColor.toLegacy("#ff6b6b", "❤ " + currentHP + "/" + maxHP) +
                HexColor.toLegacy("#ffffff", " | ") +
                HexColor.toLegacy("#4fc3f7", "✦ " + currentMana + "/" + maxMana) +
                HexColor.toLegacy("#ffffff", " | ") +
                HexColor.toLegacy("#ffeb3b", "🔮 " + selectedSpell) +
                HexColor.toLegacy("#ffffff", " | ") +
                HexColor.toLegacy(notification.type.getColor(), notification.type.getIcon() + " " + notification.message);
            
            // Gửi action bar
            Component actionBarComponent = HexColor.createComponent(actionBarText);
            player.sendActionBar(actionBarComponent);
            
        } catch (Exception e) {
            // Nếu có lỗi, xóa notifications cho player này
            clearNotifications(player);
        }
    }
    
    /**
     * Lấy notification hiện tại của player (để ActionBarManager kiểm tra)
     */
    public BuffNotification getCurrentNotification(Player player) {
        UUID playerId = player.getUniqueId();
        Queue<BuffNotification> queue = playerNotifications.get(playerId);
        return (queue != null && !queue.isEmpty()) ? queue.peek() : null;
    }
    
    /**
     * Dọn dẹp khi plugin disable
     */
    public void shutdown() {
        // Cancel tất cả tasks
        for (BukkitTask task : displayTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        displayTasks.clear();
        playerNotifications.clear();
    }
}