name: Magic
version: 1.0.0
main: shyrcs.MagicPlugin
api-version: 1.21
author: shyrcs
description: <PERSON><PERSON> thống ma thuật phức tạp với 10 nguyên tố

commands:
  magic:
    description: <PERSON><PERSON><PERSON> chính của magic plugin
    usage: /<command> givewand <player> <wandType>
    permission: magic.command
  profile:
    description: Mở profile người chơi
    usage: /<command>
    permission: magic.profile
  elemental:
    description: Quản lý elemental status
    usage: /<command> [apply|clear|test] [element] [duration]
    permission: magic.elemental
  actionbar:
    description: Quản lý action bar
    usage: /<command> [on|off|reload|status]
    permission: magic.actionbar
  debugwand:
    description: Debug thông tin đũa phép
    usage: /<command>
    permission: magic.debug
  testspell:
    description: Test spell system
    usage: /<command> [set|chant] [spellname]
    permission: magic.debug
  mana:
    description: Quản lý mana
    usage: /<command> [set|add|full|empty] [amount]
    permission: magic.mana
  texttest:
    description: Test MiniMessage formatting
    usage: /<command> [basic|shadow|item]
    permission: magic.texttest
  combo:
    description: Xem thông tin combo và spell history
    usage: /<command> [history|clear]
    permission: magic.combo
  smartcombo:
    description: Test Smart Combo Detection System
    usage: /<command> [demo1|demo2|demo3|reset]
    permission: magic.debug

permissions:
  magic.*:
    description: Toàn quyền magic
    default: op
  magic.command:
    description: Sử dụng lệnh magic
    default: true
  magic.profile:
    description: Xem profile
    default: true
  magic.elemental:
    description: Chọn nguyên tố
    default: true
  magic.actionbar:
    description: Quản lý action bar
    default: true
  magic.debug:
    description: Debug commands
    default: op
  magic.mana:
    description: Quản lý mana
    default: true
  magic.texttest:
    description: Test MiniMessage formatting
    default: true
  magic.combo:
    description: Xem thông tin combo
    default: true