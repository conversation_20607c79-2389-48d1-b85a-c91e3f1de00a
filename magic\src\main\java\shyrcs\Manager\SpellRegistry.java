package shyrcs.Manager;

import shyrcs.Magic.Spell;
import shyrcs.Magic.Pyro.Novice.FlameLance;
import shyrcs.Magic.Hydro.Novice.WaterBolt;
import shyrcs.Magic.Anemo.Novice.WindBlade;

import java.util.*;
import java.util.logging.Logger;

/**
 * Registry quản lý tất cả phép thuật trong game
 * Tự động phát hiện và đăng ký phép thuật theo cấu trúc folder
 */
public class SpellRegistry {
    
    private final Map<String, Spell> spellMap = new HashMap<>();
    private final Map<String, List<Spell>> spellsByElement = new HashMap<>();
    private final Map<String, List<Spell>> spellsByRank = new HashMap<>();
    private final Logger logger;
    
    // Danh sách 10 nguyên tố
    public static final String[] ELEMENTS = {
        "Pyro", "Hydro", "Anemo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 
        "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Dark", "Quantum"
    };
    
    // Danh sách 7 cấp độ
    public static final String[] RANKS = {
        "Novi<PERSON>", "Apprentice", "Adept", "Expert", 
        "Master", "Legendary", "Mythic"
    };
    
    public SpellRegistry() {
        this.logger = Logger.getLogger("SpellRegistry");
        initializeMaps();
        registerAllSpells();
    }
    
    /**
     * Khởi tạo maps
     */
    private void initializeMaps() {
        for (String element : ELEMENTS) {
            spellsByElement.put(element, new ArrayList<>());
        }
        
        for (String rank : RANKS) {
            spellsByRank.put(rank, new ArrayList<>());
        }
    }
    
    /**
     * Đăng ký tất cả phép thuật
     */
    private void registerAllSpells() {
        logger.info("Đang đăng ký phép thuật...");
        
        // Đăng ký phép Pyro
        registerSpell(new FlameLance());
        
        // Đăng ký phép Hydro (placeholder)
        registerSpell(new WaterBolt());
        
        // Đăng ký phép Anemo (placeholder)
        registerSpell(new WindBlade());
        
        // TODO: Thêm các phép thuật khác khi được implement
        
        logger.info("Đã đăng ký " + spellMap.size() + " phép thuật!");
        logStatistics();
    }
    
    /**
     * Đăng ký một phép thuật
     */
    public void registerSpell(Spell spell) {
        String name = spell.getName();
        String element = spell.getElement();
        String rank = spell.getRank();
        String lowerName = name.toLowerCase();

        // Thêm vào map chính
        spellMap.put(lowerName, spell);

        // Thêm vào map theo nguyên tố
        spellsByElement.get(element).add(spell);

        // Thêm vào map theo cấp độ
        spellsByRank.get(rank).add(spell);

        System.out.println("DEBUG - Registered spell: '" + name + "' with key: '" + lowerName + "'");
        logger.info("Đã đăng ký: " + name + " (" + element + " - " + rank + ")");
    }
    
    /**
     * Lấy phép thuật theo tên
     */
    public Spell getSpell(String name) {
        String lowerName = name.toLowerCase();
        Spell spell = spellMap.get(lowerName);

        System.out.println("DEBUG - SpellRegistry.getSpell('" + name + "') -> key: '" + lowerName + "' -> result: " + spell);
        System.out.println("DEBUG - Available spells in registry: " + spellMap.keySet());

        return spell;
    }
    
    /**
     * Lấy danh sách phép theo nguyên tố
     */
    public List<Spell> getSpellsByElement(String element) {
        return new ArrayList<>(spellsByElement.getOrDefault(element, new ArrayList<>()));
    }
    
    /**
     * Lấy danh sách phép theo cấp độ
     */
    public List<Spell> getSpellsByRank(String rank) {
        return new ArrayList<>(spellsByRank.getOrDefault(rank, new ArrayList<>()));
    }
    
    /**
     * Lấy tất cả phép thuật
     */
    public Collection<Spell> getAllSpells() {
        return new ArrayList<>(spellMap.values());
    }
    
    /**
     * Kiểm tra phép thuật có tồn tại
     */
    public boolean hasSpell(String name) {
        return spellMap.containsKey(name.toLowerCase());
    }
    
    /**
     * Lấy thông tin phép thuật
     */
    public SpellInfo getSpellInfo(String name) {
        Spell spell = getSpell(name);
        if (spell == null) return null;
        
        return new SpellInfo(
            spell.getName(),
            spell.getDescription(),
            spell.getElement(),
            spell.getRank(),
            spell.getManaCost(),
            spell.getCooldown(),
            spell.getVelkathRune(),
            spell.getChant()
        );
    }
    
    /**
     * In thống kê
     */
    private void logStatistics() {
        logger.info("=== THỐNG KÊ PHÉP THUẬT ===");
        
        for (String element : ELEMENTS) {
            int count = spellsByElement.get(element).size();
            if (count > 0) {
                logger.info(element + ": " + count + " phép");
            }
        }
        
        logger.info("Tổng cộng: " + spellMap.size() + " phép thuật");
        logger.info("========================");
    }
    
    /**
     * Class chứa thông tin phép thuật
     */
    public static class SpellInfo {
        public final String name;
        public final String description;
        public final String element;
        public final String rank;
        public final int manaCost;
        public final long cooldown;
        public final String rune;
        public final String chant;
        
        public SpellInfo(String name, String description, String element, String rank, 
                        int manaCost, long cooldown, String rune, String chant) {
            this.name = name;
            this.description = description;
            this.element = element;
            this.rank = rank;
            this.manaCost = manaCost;
            this.cooldown = cooldown;
            this.rune = rune;
            this.chant = chant;
        }
    }
}
